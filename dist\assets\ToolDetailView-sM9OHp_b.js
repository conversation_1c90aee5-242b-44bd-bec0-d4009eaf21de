import{b as a,s as l,_ as e,u as s,d as t,c as i}from"./index-DdsLZGoA.js";import{d as n,r as o,c as r,o as c,m as u,q as v,C as d,F as p,z as g,y as h,u as y,a6 as f,H as m,K as k,A as _,B as w,N as b,E as C,t as x,G as $,aR as I,O as M,L as q,aC as D,aD as L}from"./vendor-VsOxy-e0.js";const T={class:"tool-rating"},z={class:"rating-header"},A={class:"rating-summary"},H={class:"average-rating"},V={class:"rating-score"},j={class:"stars"},E={class:"rating-count"},F={class:"rating-distribution"},N={class:"rating-label"},R={class:"bar-container"},U={class:"rating-count"},W={key:0,class:"user-rating-section"},B={class:"rating-input"},G={class:"stars-input"},K={class:"rating-text"},O={class:"rating-actions"},S={class:"anonymous-checkbox"},J=["disabled"],P={key:1,class:"login-prompt"},Q={class:"reviews-section"},X={key:0,class:"no-reviews"},Y={key:1,class:"reviews-list"},Z={class:"review-header"},aa={class:"reviewer-info"},la={class:"reviewer-name"},ea={class:"review-rating"},sa={class:"review-date"},ta={key:0,class:"review-content"},ia=e(n({__name:"ToolRating",props:{toolId:{}},setup(e){const s=e,t=a(),i=o([]),n=o([]),I=o(null),M=o(0),q=o(0),D=o(""),L=o(!1),ia=o(!1),na=r(()=>{if(0===i.value.length)return 0;return i.value.reduce((a,l)=>a+l.rating,0)/i.value.length}),oa=r(()=>i.value.length),ra=r(()=>{const a={5:0,4:0,3:0,2:0,1:0};return i.value.forEach(l=>{a[l.rating]++}),a});c(async()=>{await ca(),await ua()});const ca=async()=>{try{const{data:a,error:e}=await l.from("tool_ratings").select("\n        *,\n        user_profiles (username)\n      ").eq("tool_id",s.toolId).order("created_at",{ascending:!1});if(e)throw e;i.value=a||[],n.value=(null==a?void 0:a.filter(a=>a.review))||[]}catch(a){console.error("加载评分失败:",a)}},ua=async()=>{if(t.user)try{const{data:a,error:e}=await l.from("tool_ratings").select("*").eq("tool_id",s.toolId).eq("user_id",t.user.id).single();a&&(I.value=a,M.value=a.rating,D.value=a.review||"",L.value=a.is_anonymous)}catch(a){}},va=a=>0===oa.value?0:a/oa.value*100,da=async()=>{if(M.value&&t.user)try{ia.value=!0;const a={tool_id:s.toolId,user_id:t.user.id,rating:M.value,review:D.value.trim()||null,is_anonymous:L.value};if(I.value){const{error:e}=await l.from("tool_ratings").update(a).eq("id",I.value.id);if(e)throw e}else{const{error:e}=await l.from("tool_ratings").insert(a);if(e)throw e}await ca(),await ua()}catch(a){console.error("提交评分失败:",a),alert("提交失败，请重试")}finally{ia.value=!1}};return(a,l)=>{return $(),u("div",T,[v("div",z,[l[4]||(l[4]=v("h3",null,"用户评价",-1)),v("div",A,[v("div",H,[v("span",V,d(na.value.toFixed(1)),1),v("div",j,[($(),u(p,null,g(5,a=>h(y(f),{key:a,class:m([{filled:a<=Math.round(na.value)},"star"])},null,8,["class"])),64))]),v("span",E,"("+d(oa.value)+" 评价)",1)])])]),v("div",F,[($(!0),u(p,null,g(ra.value,(a,l)=>($(),u("div",{key:l,class:"rating-bar"},[v("span",N,d(l)+"星",1),v("div",R,[v("div",{class:"bar",style:k({width:va(a)+"%"})},null,4)]),v("span",U,d(a),1)]))),128))]),y(t).isAuthenticated?($(),u("div",W,[v("h4",null,d(I.value?"修改评价":"添加评价"),1),v("div",B,[v("div",G,[($(),u(p,null,g(5,a=>h(y(f),{key:a,class:m([{filled:a<=M.value,hover:a<=q.value},"star clickable"]),onClick:l=>{return e=a,void(M.value=e);var e},onMouseenter:l=>q.value=a,onMouseleave:l[0]||(l[0]=a=>q.value=0)},null,8,["class","onClick","onMouseenter"])),64))]),v("span",K,d((e=M.value,["","很差","一般","不错","很好","极佳"][e]||"")),1)]),_(v("textarea",{"onUpdate:modelValue":l[1]||(l[1]=a=>D.value=a),placeholder:"分享您的使用体验（可选）",class:"review-input",rows:"3"},null,512),[[w,D.value]]),v("div",O,[v("label",S,[_(v("input",{type:"checkbox","onUpdate:modelValue":l[2]||(l[2]=a=>L.value=a)},null,512),[[C,L.value]]),l[5]||(l[5]=b(" 匿名评价 "))]),v("button",{onClick:da,disabled:!M.value||ia.value,class:"submit-button"},d(ia.value?"提交中...":I.value?"更新评价":"提交评价"),9,J)])])):($(),u("div",P,[l[6]||(l[6]=v("p",null,"登录后可以评价工具",-1)),v("button",{onClick:l[3]||(l[3]=l=>a.$router.push("/auth/login")),class:"login-button"},"立即登录")])),v("div",Q,[v("h4",null,"用户评价 ("+d(n.value.length)+")",1),0===n.value.length?($(),u("div",X," 暂无评价，成为第一个评价者吧！ ")):($(),u("div",Y,[($(!0),u(p,null,g(n.value,a=>{var l,e;return $(),u("div",{key:a.id,class:"review-item"},[v("div",Z,[v("div",aa,[v("span",la,d(a.is_anonymous?"匿名用户":(null==(l=a.user_profiles)?void 0:l.username)||"用户"),1),v("div",ea,[($(),u(p,null,g(5,l=>h(y(f),{key:l,class:m([{filled:l<=a.rating},"star small"])},null,8,["class"])),64))])]),v("span",sa,d((e=a.created_at,new Date(e).toLocaleDateString("zh-CN"))),1)]),a.review?($(),u("p",ta,d(a.review),1)):x("",!0)])}),128))]))])]);var e}}}),[["__scopeId","data-v-a1280faf"]]),na={class:"tool-detail-view"},oa={class:"container"},ra={key:0,class:"tool-detail-card"},ca={class:"tool-header"},ua={class:"tool-icon"},va={class:"tool-info"},da={class:"tool-name"},pa={class:"tool-description"},ga={class:"tool-meta"},ha={class:"category"},ya={class:"clicks"},fa={class:"tool-actions"},ma={key:0,class:"features-section"},ka={class:"features-grid"},_a={key:1,class:"tutorial-section"},wa=["innerHTML"],ba={key:2,class:"video-section"},Ca={class:"video-container"},xa=["src"],$a={key:3,class:"pros-cons-section"},Ia={class:"pros-cons-grid"},Ma={class:"pros"},qa={class:"cons"},Da={key:4,class:"pricing-section"},La={class:"pricing-content"},Ta={class:"related-tools-section"},za={class:"related-tools-grid"},Aa=["onClick"],Ha={class:"tool-icon"},Va={class:"tool-name"},ja={key:1,class:"loading-state"},Ea={key:2,class:"error-state"},Fa=e(n({__name:"ToolDetailView",setup(l){const e=t(),n=i(),k=s(),_=a(),w=o(null),C=o(!0),T=o([]),z=r(()=>!1);c(async()=>{await A()});const A=async()=>{try{C.value=!0;const a=e.params.id;await k.initialize(),w.value=k.tools.find(l=>l.id===a),w.value&&(T.value=k.tools.filter(a=>a.category_id===w.value.category_id&&a.id!==w.value.id).slice(0,4))}catch(a){console.error("加载工具详情失败:",a)}finally{C.value=!1}},H=()=>{n.back()},V=async()=>{var a;if(null==(a=w.value)?void 0:a.url)try{let a=w.value.url.trim();a.startsWith("http://")||a.startsWith("https://")||(a="https://"+a),await k.incrementClickCount(w.value.id),window.open(a,"_blank","noopener,noreferrer")}catch(l){console.error("打开链接失败:",l),alert("无法打开该链接")}else alert("该工具暂无可用链接")},j=()=>{_.isAuthenticated||n.push("/auth/login")};return(a,l)=>{var e,s;return $(),u("div",na,[v("div",oa,[v("button",{onClick:H,class:"back-button"},[h(y(I),{class:"icon"}),l[0]||(l[0]=b(" 返回工具列表 "))]),w.value?($(),u("div",ra,[v("div",ca,[v("div",ua,d(w.value.icon||"🔧"),1),v("div",va,[v("h1",da,d(w.value.name),1),v("p",pa,d(w.value.description),1),v("div",ga,[v("span",ha,d(null==(e=w.value.categories)?void 0:e.name),1),v("span",ya,d(w.value.click_count)+" 次使用",1)])]),v("div",fa,[v("button",{onClick:V,class:"primary-button"},[h(y(M),{class:"icon"}),l[1]||(l[1]=b(" 访问工具 "))]),v("button",{onClick:j,class:m(["favorite-button",{favorited:z.value}])},[h(y(f),{class:"icon"}),b(" "+d(z.value?"已收藏":"收藏"),1)],2)])]),w.value.features?($(),u("div",ma,[l[2]||(l[2]=v("h2",null,"主要功能",-1)),v("div",ka,[($(!0),u(p,null,g(w.value.features,a=>($(),u("div",{key:a,class:"feature-item"},[h(y(q),{class:"icon"}),b(" "+d(a),1)]))),128))])])):x("",!0),w.value.tutorial_content?($(),u("div",_a,[l[3]||(l[3]=v("h2",null,"使用教程",-1)),v("div",{class:"tutorial-content",innerHTML:(s=w.value.tutorial_content,s.replace(/### (.*)/g,"<h3>$1</h3>").replace(/## (.*)/g,"<h2>$1</h2>").replace(/# (.*)/g,"<h1>$1</h1>").replace(/\*\*(.*?)\*\*/g,"<strong>$1</strong>").replace(/\*(.*?)\*/g,"<em>$1</em>").replace(/\n/g,"<br>"))},null,8,wa)])):x("",!0),w.value.tutorial_video_url?($(),u("div",ba,[l[4]||(l[4]=v("h2",null,"视频教程",-1)),v("div",Ca,[v("iframe",{src:w.value.tutorial_video_url,frameborder:"0",allowfullscreen:""},null,8,xa)])])):x("",!0),w.value.pros_cons?($(),u("div",$a,[l[7]||(l[7]=v("h2",null,"优缺点分析",-1)),v("div",Ia,[v("div",Ma,[v("h3",null,[h(y(D),{class:"icon"}),l[5]||(l[5]=b(" 优点"))]),v("ul",null,[($(!0),u(p,null,g(w.value.pros_cons.pros,a=>($(),u("li",{key:a},d(a),1))),128))])]),v("div",qa,[v("h3",null,[h(y(L),{class:"icon"}),l[6]||(l[6]=b(" 缺点"))]),v("ul",null,[($(!0),u(p,null,g(w.value.pros_cons.cons,a=>($(),u("li",{key:a},d(a),1))),128))])])])])):x("",!0),w.value.pricing_info?($(),u("div",Da,[l[8]||(l[8]=v("h2",null,"价格信息",-1)),v("div",La,d(w.value.pricing_info),1)])):x("",!0),h(ia,{"tool-id":w.value.id},null,8,["tool-id"]),v("div",Ta,[l[9]||(l[9]=v("h2",null,"相关工具推荐",-1)),v("div",za,[($(!0),u(p,null,g(T.value,a=>($(),u("div",{key:a.id,class:"related-tool-card",onClick:l=>{return e=a.id,void n.push(`/tools/${e}`);var e}},[v("div",Ha,d(a.icon||"🔧"),1),v("div",Va,d(a.name),1)],8,Aa))),128))])])])):C.value?($(),u("div",ja,l[10]||(l[10]=[v("div",{class:"spinner"},null,-1),v("p",null,"正在加载工具详情...",-1)]))):($(),u("div",Ea,[l[11]||(l[11]=v("p",null,"工具不存在或加载失败",-1)),v("button",{onClick:H,class:"secondary-button"},"返回")]))])])}}}),[["__scopeId","data-v-8bc771c3"]]);export{Fa as default};
