const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./EnhancedHomeView-DdqcJPLZ.js","./vendor-VsOxy-e0.js","./EnhancedSearchBox-BmYSxjit.js","./EnhancedSearchBox-9Y3MuIoY.css","./EnhancedHomeView-BvBI8rQk.css","./SearchResultsView-DqcTq4xp.js","./SearchResultsView-BkLEblh8.css","./HelpSupportView-DWIQ7VAC.js","./HelpSupportView-BKaAOmdZ.css","./FAQView-BFRoM9xK.js","./UserGuideView-DQU9NIhA.js","./FeedbackView-Bw5b4gym.js","./ContactView-CkEI2Syf.js","./TermsOfServiceView-CXpvxheH.js","./ProductUploadView-DRXY1Ej5.js","./ToolsView-CTBVCbDK.js","./ToolsView-66CnP_l1.css","./ToolDetailView-sM9OHp_b.js","./ToolDetailView-BtAR-WP9.css","./ProductsView-U3FaM-Hu.js","./ProductsView-BMOkDPs_.css","./ProductDetailView-izR7Is7_.js","./ProductDetailView-CpYh6O1o.css","./UserView-cTq0MBXA.js","./UserView-BGzqa1ua.css","./ProfileView-37yX491w.js","./ProfileView-DpPtkwxE.css","./FavoritesView-B8dHQ6IJ.js","./FavoritesView-RzeM2c7T.css","./OrdersView-MKsWNrft.js","./OrdersView-DzrdDkRi.css","./AuthView-DRKopJy2.js","./AuthView-kJTw_t9R.css","./LoginView-DwHD2DlN.js","./LoginView-DqUiEiAL.css","./RegisterView-DJqFKpGO.js","./RegisterView-BfPJesmG.css","./ForgotPasswordView-BsEquy06.js","./ForgotPasswordView-CsrSL9dm.css","./AdminView-BHanQEuE.js","./AdminView-CyAVogqa.css","./DashboardView-BK5pYTOp.js","./DashboardView-CkbHANYK.css","./AdminToolsView-CVgUrrhD.js","./AdminToolsView-CnEPbFPs.css","./ProductManagementView-BOjebqFo.js","./SettingsView-_VYKdPFi.js","./SettingsView-CVXWM9Ai.css","./PaymentView-CW7EphiN.js","./PaymentView-DVMQfdsh.css","./PaymentSuccessView-BieT_M2W.js","./PaymentSuccessView-BGoPpA6X.css","./PaymentCancelView-CzRWOnua.js","./PaymentCancelView-5StAoE7K.css","./SimpleSettingsView-COGravRk.js","./SimpleSettingsView-BSLqxQ8l.css","./TestSettingsView-DuhMWWdo.js","./TestSettingsView-ecQUovCY.css","./NotFoundView-C9ngCMrI.js","./NotFoundView-J7iPsJwS.css"])))=>i.map(i=>d[i]);
var e=Object.defineProperty,t=(t,s,r)=>((t,s,r)=>s in t?e(t,s,{enumerable:!0,configurable:!0,writable:!0,value:r}):t[s]=r)(t,"symbol"!=typeof s?s+"":s,r);import{s,d as r,u as n,a as i,c as a,p as o,r as l,w as c,h as u,n as d,i as h,b as f,e as p,o as m,g as v,f as g,j as y,k as b,l as _,m as w,q as k,t as S,v as E,S as T,M as j,x as O,y as C,X as P,F as A,z as x,A as I,B as R,C as $,D as L,E as U,G as D,H as q,I as N,J as M,K as V,L as B,N as F,O as z,P as W,Q as J,R as G,T as K,U as H,V as Q,W as Y,Y as Z,Z as X,_ as ee,$ as te,a0 as se,a1 as re,a2 as ne,a3 as ie,a4 as ae,a5 as oe,a6 as le,a7 as ce,a8 as ue,a9 as de,aa as he,ab as fe,ac as pe,ad as me,ae as ve,af as ge,ag as ye,ah as be,ai as _e,aj as we,ak as ke,al as Se,am as Ee,an as Te,ao as je,ap as Oe}from"./vendor-VsOxy-e0.js";!function(){const e=document.createElement("link").relList;if(!(e&&e.supports&&e.supports("modulepreload"))){for(const e of document.querySelectorAll('link[rel="modulepreload"]'))t(e);new MutationObserver(e=>{for(const s of e)if("childList"===s.type)for(const e of s.addedNodes)"LINK"===e.tagName&&"modulepreload"===e.rel&&t(e)}).observe(document,{childList:!0,subtree:!0})}function t(e){if(e.ep)return;e.ep=!0;const t=function(e){const t={};return e.integrity&&(t.integrity=e.integrity),e.referrerPolicy&&(t.referrerPolicy=e.referrerPolicy),"use-credentials"===e.crossOrigin?t.credentials="include":"anonymous"===e.crossOrigin?t.credentials="omit":t.credentials="same-origin",t}(e);fetch(e.href,t)}}();
/*!
  * vue-router v4.5.1
  * (c) 2025 Eduardo San Martin Morote
  * @license MIT
  */
const Ce="undefined"!=typeof document;function Pe(e){return"object"==typeof e||"displayName"in e||"props"in e||"__vccOpts"in e}const Ae=Object.assign;function xe(e,t){const s={};for(const r in t){const n=t[r];s[r]=Re(n)?n.map(e):e(n)}return s}const Ie=()=>{},Re=Array.isArray,$e=/#/g,Le=/&/g,Ue=/\//g,De=/=/g,qe=/\?/g,Ne=/\+/g,Me=/%5B/g,Ve=/%5D/g,Be=/%5E/g,Fe=/%60/g,ze=/%7B/g,We=/%7C/g,Je=/%7D/g,Ge=/%20/g;function Ke(e){return encodeURI(""+e).replace(We,"|").replace(Me,"[").replace(Ve,"]")}function He(e){return Ke(e).replace(Ne,"%2B").replace(Ge,"+").replace($e,"%23").replace(Le,"%26").replace(Fe,"`").replace(ze,"{").replace(Je,"}").replace(Be,"^")}function Qe(e){return He(e).replace(De,"%3D")}function Ye(e){return null==e?"":function(e){return Ke(e).replace($e,"%23").replace(qe,"%3F")}(e).replace(Ue,"%2F")}function Ze(e){try{return decodeURIComponent(""+e)}catch(t){}return""+e}const Xe=/\/$/;function et(e,t,s="/"){let r,n={},i="",a="";const o=t.indexOf("#");let l=t.indexOf("?");return o<l&&o>=0&&(l=-1),l>-1&&(r=t.slice(0,l),i=t.slice(l+1,o>-1?o:t.length),n=e(i)),o>-1&&(r=r||t.slice(0,o),a=t.slice(o,t.length)),r=function(e,t){if(e.startsWith("/"))return e;if(!e)return t;const s=t.split("/"),r=e.split("/"),n=r[r.length-1];".."!==n&&"."!==n||r.push("");let i,a,o=s.length-1;for(i=0;i<r.length;i++)if(a=r[i],"."!==a){if(".."!==a)break;o>1&&o--}return s.slice(0,o).join("/")+"/"+r.slice(i).join("/")}(null!=r?r:t,s),{fullPath:r+(i&&"?")+i+a,path:r,query:n,hash:Ze(a)}}function tt(e,t){return t&&e.toLowerCase().startsWith(t.toLowerCase())?e.slice(t.length)||"/":e}function st(e,t){return(e.aliasOf||e)===(t.aliasOf||t)}function rt(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(!nt(e[s],t[s]))return!1;return!0}function nt(e,t){return Re(e)?it(e,t):Re(t)?it(t,e):e===t}function it(e,t){return Re(t)?e.length===t.length&&e.every((e,s)=>e===t[s]):1===e.length&&e[0]===t}const at={path:"/",name:void 0,params:{},query:{},hash:"",fullPath:"/",matched:[],meta:{},redirectedFrom:void 0};var ot,lt,ct,ut;function dt(e){if(!e)if(Ce){const t=document.querySelector("base");e=(e=t&&t.getAttribute("href")||"/").replace(/^\w+:\/\/[^\/]+/,"")}else e="/";return"/"!==e[0]&&"#"!==e[0]&&(e="/"+e),e.replace(Xe,"")}(lt=ot||(ot={})).pop="pop",lt.push="push",(ut=ct||(ct={})).back="back",ut.forward="forward",ut.unknown="";const ht=/^[^#]+#/;function ft(e,t){return e.replace(ht,"#")+t}const pt=()=>({left:window.scrollX,top:window.scrollY});function mt(e){let t;if("el"in e){const s=e.el,r="string"==typeof s&&s.startsWith("#"),n="string"==typeof s?r?document.getElementById(s.slice(1)):document.querySelector(s):s;if(!n)return;t=function(e,t){const s=document.documentElement.getBoundingClientRect(),r=e.getBoundingClientRect();return{behavior:t.behavior,left:r.left-s.left-(t.left||0),top:r.top-s.top-(t.top||0)}}(n,e)}else t=e;"scrollBehavior"in document.documentElement.style?window.scrollTo(t):window.scrollTo(null!=t.left?t.left:window.scrollX,null!=t.top?t.top:window.scrollY)}function vt(e,t){return(history.state?history.state.position-t:-1)+e}const gt=new Map;function yt(e,t){const{pathname:s,search:r,hash:n}=t,i=e.indexOf("#");if(i>-1){let t=n.includes(e.slice(i))?e.slice(i).length:1,s=n.slice(t);return"/"!==s[0]&&(s="/"+s),tt(s,"")}return tt(s,e)+r+n}function bt(e,t,s,r=!1,n=!1){return{back:e,current:t,forward:s,replaced:r,position:window.history.length,scroll:n?pt():null}}function _t(e){const{history:t,location:s}=window,r={value:yt(e,s)},n={value:t.state};function i(r,i,a){const o=e.indexOf("#"),l=o>-1?(s.host&&document.querySelector("base")?e:e.slice(o))+r:location.protocol+"//"+location.host+e+r;try{t[a?"replaceState":"pushState"](i,"",l),n.value=i}catch(c){console.error(c),s[a?"replace":"assign"](l)}}return n.value||i(r.value,{back:null,current:r.value,forward:null,position:t.length-1,replaced:!0,scroll:null},!0),{location:r,state:n,push:function(e,s){const a=Ae({},n.value,t.state,{forward:e,scroll:pt()});i(a.current,a,!0),i(e,Ae({},bt(r.value,e,null),{position:a.position+1},s),!1),r.value=e},replace:function(e,s){i(e,Ae({},t.state,bt(n.value.back,e,n.value.forward,!0),s,{position:n.value.position}),!0),r.value=e}}}function wt(e){return"string"==typeof e||"symbol"==typeof e}const kt=Symbol("");var St,Et;function Tt(e,t){return Ae(new Error,{type:e,[kt]:!0},t)}function jt(e,t){return e instanceof Error&&kt in e&&(null==t||!!(e.type&t))}(Et=St||(St={}))[Et.aborted=4]="aborted",Et[Et.cancelled=8]="cancelled",Et[Et.duplicated=16]="duplicated";const Ot="[^/]+?",Ct={sensitive:!1,strict:!1,start:!0,end:!0},Pt=/[.+*?^${}()[\]/\\]/g;function At(e,t){let s=0;for(;s<e.length&&s<t.length;){const r=t[s]-e[s];if(r)return r;s++}return e.length<t.length?1===e.length&&80===e[0]?-1:1:e.length>t.length?1===t.length&&80===t[0]?1:-1:0}function xt(e,t){let s=0;const r=e.score,n=t.score;for(;s<r.length&&s<n.length;){const e=At(r[s],n[s]);if(e)return e;s++}if(1===Math.abs(n.length-r.length)){if(It(r))return 1;if(It(n))return-1}return n.length-r.length}function It(e){const t=e[e.length-1];return e.length>0&&t[t.length-1]<0}const Rt={type:0,value:""},$t=/[a-zA-Z0-9_]/;function Lt(e,t,s){const r=function(e,t){const s=Ae({},Ct,t),r=[];let n=s.start?"^":"";const i=[];for(const l of e){const e=l.length?[]:[90];s.strict&&!l.length&&(n+="/");for(let t=0;t<l.length;t++){const r=l[t];let a=40+(s.sensitive?.25:0);if(0===r.type)t||(n+="/"),n+=r.value.replace(Pt,"\\$&"),a+=40;else if(1===r.type){const{value:e,repeatable:s,optional:c,regexp:u}=r;i.push({name:e,repeatable:s,optional:c});const d=u||Ot;if(d!==Ot){a+=10;try{new RegExp(`(${d})`)}catch(o){throw new Error(`Invalid custom RegExp for param "${e}" (${d}): `+o.message)}}let h=s?`((?:${d})(?:/(?:${d}))*)`:`(${d})`;t||(h=c&&l.length<2?`(?:/${h})`:"/"+h),c&&(h+="?"),n+=h,a+=20,c&&(a+=-8),s&&(a+=-20),".*"===d&&(a+=-50)}e.push(a)}r.push(e)}if(s.strict&&s.end){const e=r.length-1;r[e][r[e].length-1]+=.7000000000000001}s.strict||(n+="/?"),s.end?n+="$":s.strict&&!n.endsWith("/")&&(n+="(?:/|$)");const a=new RegExp(n,s.sensitive?"":"i");return{re:a,score:r,keys:i,parse:function(e){const t=e.match(a),s={};if(!t)return null;for(let r=1;r<t.length;r++){const e=t[r]||"",n=i[r-1];s[n.name]=e&&n.repeatable?e.split("/"):e}return s},stringify:function(t){let s="",r=!1;for(const n of e){r&&s.endsWith("/")||(s+="/"),r=!1;for(const e of n)if(0===e.type)s+=e.value;else if(1===e.type){const{value:i,repeatable:a,optional:o}=e,l=i in t?t[i]:"";if(Re(l)&&!a)throw new Error(`Provided param "${i}" is an array but it is not repeatable (* or + modifiers)`);const c=Re(l)?l.join("/"):l;if(!c){if(!o)throw new Error(`Missing required param "${i}"`);n.length<2&&(s.endsWith("/")?s=s.slice(0,-1):r=!0)}s+=c}}return s||"/"}}}(function(e){if(!e)return[[]];if("/"===e)return[[Rt]];if(!e.startsWith("/"))throw new Error(`Invalid path "${e}"`);function t(e){throw new Error(`ERR (${s})/"${c}": ${e}`)}let s=0,r=s;const n=[];let i;function a(){i&&n.push(i),i=[]}let o,l=0,c="",u="";function d(){c&&(0===s?i.push({type:0,value:c}):1===s||2===s||3===s?(i.length>1&&("*"===o||"+"===o)&&t(`A repeatable param (${c}) must be alone in its segment. eg: '/:ids+.`),i.push({type:1,value:c,regexp:u,repeatable:"*"===o||"+"===o,optional:"*"===o||"?"===o})):t("Invalid state to consume buffer"),c="")}function h(){c+=o}for(;l<e.length;)if(o=e[l++],"\\"!==o||2===s)switch(s){case 0:"/"===o?(c&&d(),a()):":"===o?(d(),s=1):h();break;case 4:h(),s=r;break;case 1:"("===o?s=2:$t.test(o)?h():(d(),s=0,"*"!==o&&"?"!==o&&"+"!==o&&l--);break;case 2:")"===o?"\\"==u[u.length-1]?u=u.slice(0,-1)+o:s=3:u+=o;break;case 3:d(),s=0,"*"!==o&&"?"!==o&&"+"!==o&&l--,u="";break;default:t("Unknown state")}else r=s,s=4;return 2===s&&t(`Unfinished custom RegExp for param "${c}"`),d(),a(),n}(e.path),s),n=Ae(r,{record:e,parent:t,children:[],alias:[]});return t&&!n.record.aliasOf==!t.record.aliasOf&&t.children.push(n),n}function Ut(e,t){const s=[],r=new Map;function n(e,s,r){const o=!r,l=qt(e);l.aliasOf=r&&r.record;const c=Bt(t,e),u=[l];if("alias"in e){const t="string"==typeof e.alias?[e.alias]:e.alias;for(const e of t)u.push(qt(Ae({},l,{components:r?r.record.components:l.components,path:e,aliasOf:r?r.record:l})))}let d,h;for(const t of u){const{path:u}=t;if(s&&"/"!==u[0]){const e=s.record.path,r="/"===e[e.length-1]?"":"/";t.path=s.record.path+(u&&r+u)}if(d=Lt(t,s,c),r?r.alias.push(d):(h=h||d,h!==d&&h.alias.push(d),o&&e.name&&!Mt(d)&&i(e.name)),Ft(d)&&a(d),l.children){const e=l.children;for(let t=0;t<e.length;t++)n(e[t],d,r&&r.children[t])}r=r||d}return h?()=>{i(h)}:Ie}function i(e){if(wt(e)){const t=r.get(e);t&&(r.delete(e),s.splice(s.indexOf(t),1),t.children.forEach(i),t.alias.forEach(i))}else{const t=s.indexOf(e);t>-1&&(s.splice(t,1),e.record.name&&r.delete(e.record.name),e.children.forEach(i),e.alias.forEach(i))}}function a(e){const t=function(e,t){let s=0,r=t.length;for(;s!==r;){const n=s+r>>1;xt(e,t[n])<0?r=n:s=n+1}const n=function(e){let t=e;for(;t=t.parent;)if(Ft(t)&&0===xt(e,t))return t;return}(e);n&&(r=t.lastIndexOf(n,r-1));return r}(e,s);s.splice(t,0,e),e.record.name&&!Mt(e)&&r.set(e.record.name,e)}return t=Bt({strict:!1,end:!0,sensitive:!1},t),e.forEach(e=>n(e)),{addRoute:n,resolve:function(e,t){let n,i,a,o={};if("name"in e&&e.name){if(n=r.get(e.name),!n)throw Tt(1,{location:e});a=n.record.name,o=Ae(Dt(t.params,n.keys.filter(e=>!e.optional).concat(n.parent?n.parent.keys.filter(e=>e.optional):[]).map(e=>e.name)),e.params&&Dt(e.params,n.keys.map(e=>e.name))),i=n.stringify(o)}else if(null!=e.path)i=e.path,n=s.find(e=>e.re.test(i)),n&&(o=n.parse(i),a=n.record.name);else{if(n=t.name?r.get(t.name):s.find(e=>e.re.test(t.path)),!n)throw Tt(1,{location:e,currentLocation:t});a=n.record.name,o=Ae({},t.params,e.params),i=n.stringify(o)}const l=[];let c=n;for(;c;)l.unshift(c.record),c=c.parent;return{name:a,path:i,params:o,matched:l,meta:Vt(l)}},removeRoute:i,clearRoutes:function(){s.length=0,r.clear()},getRoutes:function(){return s},getRecordMatcher:function(e){return r.get(e)}}}function Dt(e,t){const s={};for(const r of t)r in e&&(s[r]=e[r]);return s}function qt(e){const t={path:e.path,redirect:e.redirect,name:e.name,meta:e.meta||{},aliasOf:e.aliasOf,beforeEnter:e.beforeEnter,props:Nt(e),children:e.children||[],instances:{},leaveGuards:new Set,updateGuards:new Set,enterCallbacks:{},components:"components"in e?e.components||null:e.component&&{default:e.component}};return Object.defineProperty(t,"mods",{value:{}}),t}function Nt(e){const t={},s=e.props||!1;if("component"in e)t.default=s;else for(const r in e.components)t[r]="object"==typeof s?s[r]:s;return t}function Mt(e){for(;e;){if(e.record.aliasOf)return!0;e=e.parent}return!1}function Vt(e){return e.reduce((e,t)=>Ae(e,t.meta),{})}function Bt(e,t){const s={};for(const r in e)s[r]=r in t?t[r]:e[r];return s}function Ft({record:e}){return!!(e.name||e.components&&Object.keys(e.components).length||e.redirect)}function zt(e){const t={};if(""===e||"?"===e)return t;const s=("?"===e[0]?e.slice(1):e).split("&");for(let r=0;r<s.length;++r){const e=s[r].replace(Ne," "),n=e.indexOf("="),i=Ze(n<0?e:e.slice(0,n)),a=n<0?null:Ze(e.slice(n+1));if(i in t){let e=t[i];Re(e)||(e=t[i]=[e]),e.push(a)}else t[i]=a}return t}function Wt(e){let t="";for(let s in e){const r=e[s];if(s=Qe(s),null==r){void 0!==r&&(t+=(t.length?"&":"")+s);continue}(Re(r)?r.map(e=>e&&He(e)):[r&&He(r)]).forEach(e=>{void 0!==e&&(t+=(t.length?"&":"")+s,null!=e&&(t+="="+e))})}return t}function Jt(e){const t={};for(const s in e){const r=e[s];void 0!==r&&(t[s]=Re(r)?r.map(e=>null==e?null:""+e):null==r?r:""+r)}return t}const Gt=Symbol(""),Kt=Symbol(""),Ht=Symbol(""),Qt=Symbol(""),Yt=Symbol("");function Zt(){let e=[];return{add:function(t){return e.push(t),()=>{const s=e.indexOf(t);s>-1&&e.splice(s,1)}},list:()=>e.slice(),reset:function(){e=[]}}}function Xt(e,t,s,r,n,i=e=>e()){const a=r&&(r.enterCallbacks[n]=r.enterCallbacks[n]||[]);return()=>new Promise((o,l)=>{const c=e=>{var i;!1===e?l(Tt(4,{from:s,to:t})):e instanceof Error?l(e):"string"==typeof(i=e)||i&&"object"==typeof i?l(Tt(2,{from:t,to:e})):(a&&r.enterCallbacks[n]===a&&"function"==typeof e&&a.push(e),o())},u=i(()=>e.call(r&&r.instances[n],t,s,c));let d=Promise.resolve(u);e.length<3&&(d=d.then(c)),d.catch(e=>l(e))})}function es(e,t,s,r,n=e=>e()){const i=[];for(const a of e)for(const e in a.components){let o=a.components[e];if("beforeRouteEnter"===t||a.instances[e])if(Pe(o)){const l=(o.__vccOpts||o)[t];l&&i.push(Xt(l,s,r,a,e,n))}else{let l=o();i.push(()=>l.then(i=>{if(!i)throw new Error(`Couldn't resolve component "${e}" at "${a.path}"`);const o=(l=i).__esModule||"Module"===l[Symbol.toStringTag]||l.default&&Pe(l.default)?i.default:i;var l;a.mods[e]=i,a.components[e]=o;const c=(o.__vccOpts||o)[t];return c&&Xt(c,s,r,a,e,n)()}))}}return i}function ts(e){const t=h(Ht),s=h(Qt),r=a(()=>{const s=n(e.to);return t.resolve(s)}),i=a(()=>{const{matched:e}=r.value,{length:t}=e,n=e[t-1],i=s.matched;if(!n||!i.length)return-1;const a=i.findIndex(st.bind(null,n));if(a>-1)return a;const o=ns(e[t-2]);return t>1&&ns(n)===o&&i[i.length-1].path!==o?i.findIndex(st.bind(null,e[t-2])):a}),o=a(()=>i.value>-1&&function(e,t){for(const s in t){const r=t[s],n=e[s];if("string"==typeof r){if(r!==n)return!1}else if(!Re(n)||n.length!==r.length||r.some((e,t)=>e!==n[t]))return!1}return!0}(s.params,r.value.params)),l=a(()=>i.value>-1&&i.value===s.matched.length-1&&rt(s.params,r.value.params));return{route:r,href:a(()=>r.value.href),isActive:o,isExactActive:l,navigate:function(s={}){if(function(e){if(e.metaKey||e.altKey||e.ctrlKey||e.shiftKey)return;if(e.defaultPrevented)return;if(void 0!==e.button&&0!==e.button)return;if(e.currentTarget&&e.currentTarget.getAttribute){const t=e.currentTarget.getAttribute("target");if(/\b_blank\b/i.test(t))return}e.preventDefault&&e.preventDefault();return!0}(s)){const s=t[n(e.replace)?"replace":"push"](n(e.to)).catch(Ie);return e.viewTransition&&"undefined"!=typeof document&&"startViewTransition"in document&&document.startViewTransition(()=>s),s}return Promise.resolve()}}}const ss=r({name:"RouterLink",compatConfig:{MODE:3},props:{to:{type:[String,Object],required:!0},replace:Boolean,activeClass:String,exactActiveClass:String,custom:Boolean,ariaCurrentValue:{type:String,default:"page"},viewTransition:Boolean},useLink:ts,setup(e,{slots:t}){const s=f(ts(e)),{options:r}=h(Ht),n=a(()=>({[is(e.activeClass,r.linkActiveClass,"router-link-active")]:s.isActive,[is(e.exactActiveClass,r.linkExactActiveClass,"router-link-exact-active")]:s.isExactActive}));return()=>{const r=t.default&&(1===(i=t.default(s)).length?i[0]:i);var i;return e.custom?r:u("a",{"aria-current":s.isExactActive?e.ariaCurrentValue:null,href:s.href,onClick:s.navigate,class:n.value},r)}}}),rs=ss;function ns(e){return e?e.aliasOf?e.aliasOf.path:e.path:""}const is=(e,t,s)=>null!=e?e:null!=t?t:s;function as(e,t){if(!e)return null;const s=e(t);return 1===s.length?s[0]:s}const os=r({name:"RouterView",inheritAttrs:!1,props:{name:{type:String,default:"default"},route:Object},compatConfig:{MODE:3},setup(e,{attrs:t,slots:s}){const r=h(Yt),i=a(()=>e.route||r.value),d=h(Kt,0),f=a(()=>{let e=n(d);const{matched:t}=i.value;let s;for(;(s=t[e])&&!s.components;)e++;return e}),p=a(()=>i.value.matched[f.value]);o(Kt,a(()=>f.value+1)),o(Gt,p),o(Yt,i);const m=l();return c(()=>[m.value,p.value,e.name],([e,t,s],[r,n,i])=>{t&&(t.instances[s]=e,n&&n!==t&&e&&e===r&&(t.leaveGuards.size||(t.leaveGuards=n.leaveGuards),t.updateGuards.size||(t.updateGuards=n.updateGuards))),!e||!t||n&&st(t,n)&&r||(t.enterCallbacks[s]||[]).forEach(t=>t(e))},{flush:"post"}),()=>{const r=i.value,n=e.name,a=p.value,o=a&&a.components[n];if(!o)return as(s.default,{Component:o,route:r});const l=a.props[n],c=l?!0===l?r.params:"function"==typeof l?l(r):l:null,d=u(o,Ae({},c,t,{onVnodeUnmounted:e=>{e.component.isUnmounted&&(a.instances[n]=null)},ref:m}));return as(s.default,{Component:d,route:r})||d}}});function ls(){return h(Ht)}function cs(e){return h(Qt)}const us={},ds=function(e,t,s){let r=Promise.resolve();if(t&&t.length>0){const e=document.getElementsByTagName("link"),n=document.querySelector("meta[property=csp-nonce]"),i=(null==n?void 0:n.nonce)||(null==n?void 0:n.getAttribute("nonce"));r=Promise.allSettled(t.map(t=>{if(t=function(e,t){return new URL(e,t).href}(t,s),t in us)return;us[t]=!0;const r=t.endsWith(".css"),n=r?'[rel="stylesheet"]':"";if(!!s)for(let s=e.length-1;s>=0;s--){const n=e[s];if(n.href===t&&(!r||"stylesheet"===n.rel))return}else if(document.querySelector(`link[href="${t}"]${n}`))return;const a=document.createElement("link");return a.rel=r?"stylesheet":"modulepreload",r||(a.as="script"),a.crossOrigin="",a.href=t,i&&a.setAttribute("nonce",i),document.head.appendChild(a),r?new Promise((e,s)=>{a.addEventListener("load",e),a.addEventListener("error",()=>s(new Error(`Unable to preload CSS for ${t}`)))}):void 0}))}function n(e){const t=new Event("vite:preloadError",{cancelable:!0});if(t.payload=e,window.dispatchEvent(t),!t.defaultPrevented)throw e}return r.then(t=>{for(const e of t||[])"rejected"===e.status&&n(e.reason);return e().catch(n)})};class hs extends Error{constructor(e,t="FunctionsError",s){super(e),this.name=t,this.context=s}}class fs extends hs{constructor(e){super("Failed to send a request to the Edge Function","FunctionsFetchError",e)}}class ps extends hs{constructor(e){super("Relay Error invoking the Edge Function","FunctionsRelayError",e)}}class ms extends hs{constructor(e){super("Edge Function returned a non-2xx status code","FunctionsHttpError",e)}}var vs,gs;(gs=vs||(vs={})).Any="any",gs.ApNortheast1="ap-northeast-1",gs.ApNortheast2="ap-northeast-2",gs.ApSouth1="ap-south-1",gs.ApSoutheast1="ap-southeast-1",gs.ApSoutheast2="ap-southeast-2",gs.CaCentral1="ca-central-1",gs.EuCentral1="eu-central-1",gs.EuWest1="eu-west-1",gs.EuWest2="eu-west-2",gs.EuWest3="eu-west-3",gs.SaEast1="sa-east-1",gs.UsEast1="us-east-1",gs.UsWest1="us-west-1",gs.UsWest2="us-west-2";var ys=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};class bs{constructor(e,{headers:t={},customFetch:s,region:r=vs.Any}={}){this.url=e,this.headers=t,this.region=r,this.fetch=(e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ds(async()=>{const{default:e}=await Promise.resolve().then(()=>$s);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)})(s)}setAuth(e){this.headers.Authorization=`Bearer ${e}`}invoke(e,t={}){var s;return ys(this,void 0,void 0,function*(){try{const{headers:r,method:n,body:i}=t;let a,o={},{region:l}=t;l||(l=this.region),l&&"any"!==l&&(o["x-region"]=l),i&&(r&&!Object.prototype.hasOwnProperty.call(r,"Content-Type")||!r)&&("undefined"!=typeof Blob&&i instanceof Blob||i instanceof ArrayBuffer?(o["Content-Type"]="application/octet-stream",a=i):"string"==typeof i?(o["Content-Type"]="text/plain",a=i):"undefined"!=typeof FormData&&i instanceof FormData?a=i:(o["Content-Type"]="application/json",a=JSON.stringify(i)));const c=yield this.fetch(`${this.url}/${e}`,{method:n||"POST",headers:Object.assign(Object.assign(Object.assign({},o),this.headers),r),body:a}).catch(e=>{throw new fs(e)}),u=c.headers.get("x-relay-error");if(u&&"true"===u)throw new ps(c);if(!c.ok)throw new ms(c);let d,h=(null!==(s=c.headers.get("Content-Type"))&&void 0!==s?s:"text/plain").split(";")[0].trim();return d="application/json"===h?yield c.json():"application/octet-stream"===h?yield c.blob():"text/event-stream"===h?c:"multipart/form-data"===h?yield c.formData():yield c.text(),{data:d,error:null}}catch(r){return{data:null,error:r}}})}}var _s="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{};function ws(e){if(e.__esModule)return e;var t=e.default;if("function"==typeof t){var s=function e(){return this instanceof e?Reflect.construct(t,arguments,this.constructor):t.apply(this,arguments)};s.prototype=t.prototype}else s={};return Object.defineProperty(s,"__esModule",{value:!0}),Object.keys(e).forEach(function(t){var r=Object.getOwnPropertyDescriptor(e,t);Object.defineProperty(s,t,r.get?r:{enumerable:!0,get:function(){return e[t]}})}),s}var ks={},Ss={},Es={},Ts={},js={},Os={},Cs=function(){if("undefined"!=typeof self)return self;if("undefined"!=typeof window)return window;if("undefined"!=typeof global)return global;throw new Error("unable to locate global object")}();const Ps=Cs.fetch,As=Cs.fetch.bind(Cs),xs=Cs.Headers,Is=Cs.Request,Rs=Cs.Response,$s=Object.freeze(Object.defineProperty({__proto__:null,Headers:xs,Request:Is,Response:Rs,default:As,fetch:Ps},Symbol.toStringTag,{value:"Module"})),Ls=ws($s);var Us={};Object.defineProperty(Us,"__esModule",{value:!0});let Ds=class extends Error{constructor(e){super(e.message),this.name="PostgrestError",this.details=e.details,this.hint=e.hint,this.code=e.code}};Us.default=Ds;var qs=_s&&_s.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Os,"__esModule",{value:!0});const Ns=qs(Ls),Ms=qs(Us);Os.default=class{constructor(e){this.shouldThrowOnError=!1,this.method=e.method,this.url=e.url,this.headers=e.headers,this.schema=e.schema,this.body=e.body,this.shouldThrowOnError=e.shouldThrowOnError,this.signal=e.signal,this.isMaybeSingle=e.isMaybeSingle,e.fetch?this.fetch=e.fetch:"undefined"==typeof fetch?this.fetch=Ns.default:this.fetch=fetch}throwOnError(){return this.shouldThrowOnError=!0,this}setHeader(e,t){return this.headers=Object.assign({},this.headers),this.headers[e]=t,this}then(e,t){void 0===this.schema||(["GET","HEAD"].includes(this.method)?this.headers["Accept-Profile"]=this.schema:this.headers["Content-Profile"]=this.schema),"GET"!==this.method&&"HEAD"!==this.method&&(this.headers["Content-Type"]="application/json");let s=(0,this.fetch)(this.url.toString(),{method:this.method,headers:this.headers,body:JSON.stringify(this.body),signal:this.signal}).then(async e=>{var t,s,r;let n=null,i=null,a=null,o=e.status,l=e.statusText;if(e.ok){if("HEAD"!==this.method){const t=await e.text();""===t||(i="text/csv"===this.headers.Accept||this.headers.Accept&&this.headers.Accept.includes("application/vnd.pgrst.plan+text")?t:JSON.parse(t))}const r=null===(t=this.headers.Prefer)||void 0===t?void 0:t.match(/count=(exact|planned|estimated)/),c=null===(s=e.headers.get("content-range"))||void 0===s?void 0:s.split("/");r&&c&&c.length>1&&(a=parseInt(c[1])),this.isMaybeSingle&&"GET"===this.method&&Array.isArray(i)&&(i.length>1?(n={code:"PGRST116",details:`Results contain ${i.length} rows, application/vnd.pgrst.object+json requires 1 row`,hint:null,message:"JSON object requested, multiple (or no) rows returned"},i=null,a=null,o=406,l="Not Acceptable"):i=1===i.length?i[0]:null)}else{const t=await e.text();try{n=JSON.parse(t),Array.isArray(n)&&404===e.status&&(i=[],n=null,o=200,l="OK")}catch(c){404===e.status&&""===t?(o=204,l="No Content"):n={message:t}}if(n&&this.isMaybeSingle&&(null===(r=null==n?void 0:n.details)||void 0===r?void 0:r.includes("0 rows"))&&(n=null,o=200,l="OK"),n&&this.shouldThrowOnError)throw new Ms.default(n)}return{error:n,data:i,count:a,status:o,statusText:l}});return this.shouldThrowOnError||(s=s.catch(e=>{var t,s,r;return{error:{message:`${null!==(t=null==e?void 0:e.name)&&void 0!==t?t:"FetchError"}: ${null==e?void 0:e.message}`,details:`${null!==(s=null==e?void 0:e.stack)&&void 0!==s?s:""}`,hint:"",code:`${null!==(r=null==e?void 0:e.code)&&void 0!==r?r:""}`},data:null,count:null,status:0,statusText:""}})),s.then(e,t)}returns(){return this}overrideTypes(){return this}};var Vs=_s&&_s.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(js,"__esModule",{value:!0});const Bs=Vs(Os);let Fs=class extends Bs.default{select(e){let t=!1;const s=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!t?"":('"'===e&&(t=!t),e)).join("");return this.url.searchParams.set("select",s),this.headers.Prefer&&(this.headers.Prefer+=","),this.headers.Prefer+="return=representation",this}order(e,{ascending:t=!0,nullsFirst:s,foreignTable:r,referencedTable:n=r}={}){const i=n?`${n}.order`:"order",a=this.url.searchParams.get(i);return this.url.searchParams.set(i,`${a?`${a},`:""}${e}.${t?"asc":"desc"}${void 0===s?"":s?".nullsfirst":".nullslast"}`),this}limit(e,{foreignTable:t,referencedTable:s=t}={}){const r=void 0===s?"limit":`${s}.limit`;return this.url.searchParams.set(r,`${e}`),this}range(e,t,{foreignTable:s,referencedTable:r=s}={}){const n=void 0===r?"offset":`${r}.offset`,i=void 0===r?"limit":`${r}.limit`;return this.url.searchParams.set(n,`${e}`),this.url.searchParams.set(i,""+(t-e+1)),this}abortSignal(e){return this.signal=e,this}single(){return this.headers.Accept="application/vnd.pgrst.object+json",this}maybeSingle(){return"GET"===this.method?this.headers.Accept="application/json":this.headers.Accept="application/vnd.pgrst.object+json",this.isMaybeSingle=!0,this}csv(){return this.headers.Accept="text/csv",this}geojson(){return this.headers.Accept="application/geo+json",this}explain({analyze:e=!1,verbose:t=!1,settings:s=!1,buffers:r=!1,wal:n=!1,format:i="text"}={}){var a;const o=[e?"analyze":null,t?"verbose":null,s?"settings":null,r?"buffers":null,n?"wal":null].filter(Boolean).join("|"),l=null!==(a=this.headers.Accept)&&void 0!==a?a:"application/json";return this.headers.Accept=`application/vnd.pgrst.plan+${i}; for="${l}"; options=${o};`,this}rollback(){var e;return(null!==(e=this.headers.Prefer)&&void 0!==e?e:"").trim().length>0?this.headers.Prefer+=",tx=rollback":this.headers.Prefer="tx=rollback",this}returns(){return this}};js.default=Fs;var zs=_s&&_s.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ts,"__esModule",{value:!0});const Ws=zs(js);let Js=class extends Ws.default{eq(e,t){return this.url.searchParams.append(e,`eq.${t}`),this}neq(e,t){return this.url.searchParams.append(e,`neq.${t}`),this}gt(e,t){return this.url.searchParams.append(e,`gt.${t}`),this}gte(e,t){return this.url.searchParams.append(e,`gte.${t}`),this}lt(e,t){return this.url.searchParams.append(e,`lt.${t}`),this}lte(e,t){return this.url.searchParams.append(e,`lte.${t}`),this}like(e,t){return this.url.searchParams.append(e,`like.${t}`),this}likeAllOf(e,t){return this.url.searchParams.append(e,`like(all).{${t.join(",")}}`),this}likeAnyOf(e,t){return this.url.searchParams.append(e,`like(any).{${t.join(",")}}`),this}ilike(e,t){return this.url.searchParams.append(e,`ilike.${t}`),this}ilikeAllOf(e,t){return this.url.searchParams.append(e,`ilike(all).{${t.join(",")}}`),this}ilikeAnyOf(e,t){return this.url.searchParams.append(e,`ilike(any).{${t.join(",")}}`),this}is(e,t){return this.url.searchParams.append(e,`is.${t}`),this}in(e,t){const s=Array.from(new Set(t)).map(e=>"string"==typeof e&&new RegExp("[,()]").test(e)?`"${e}"`:`${e}`).join(",");return this.url.searchParams.append(e,`in.(${s})`),this}contains(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cs.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cs.{${t.join(",")}}`):this.url.searchParams.append(e,`cs.${JSON.stringify(t)}`),this}containedBy(e,t){return"string"==typeof t?this.url.searchParams.append(e,`cd.${t}`):Array.isArray(t)?this.url.searchParams.append(e,`cd.{${t.join(",")}}`):this.url.searchParams.append(e,`cd.${JSON.stringify(t)}`),this}rangeGt(e,t){return this.url.searchParams.append(e,`sr.${t}`),this}rangeGte(e,t){return this.url.searchParams.append(e,`nxl.${t}`),this}rangeLt(e,t){return this.url.searchParams.append(e,`sl.${t}`),this}rangeLte(e,t){return this.url.searchParams.append(e,`nxr.${t}`),this}rangeAdjacent(e,t){return this.url.searchParams.append(e,`adj.${t}`),this}overlaps(e,t){return"string"==typeof t?this.url.searchParams.append(e,`ov.${t}`):this.url.searchParams.append(e,`ov.{${t.join(",")}}`),this}textSearch(e,t,{config:s,type:r}={}){let n="";"plain"===r?n="pl":"phrase"===r?n="ph":"websearch"===r&&(n="w");const i=void 0===s?"":`(${s})`;return this.url.searchParams.append(e,`${n}fts${i}.${t}`),this}match(e){return Object.entries(e).forEach(([e,t])=>{this.url.searchParams.append(e,`eq.${t}`)}),this}not(e,t,s){return this.url.searchParams.append(e,`not.${t}.${s}`),this}or(e,{foreignTable:t,referencedTable:s=t}={}){const r=s?`${s}.or`:"or";return this.url.searchParams.append(r,`(${e})`),this}filter(e,t,s){return this.url.searchParams.append(e,`${t}.${s}`),this}};Ts.default=Js;var Gs=_s&&_s.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Es,"__esModule",{value:!0});const Ks=Gs(Ts);Es.default=class{constructor(e,{headers:t={},schema:s,fetch:r}){this.url=e,this.headers=t,this.schema=s,this.fetch=r}select(e,{head:t=!1,count:s}={}){const r=t?"HEAD":"GET";let n=!1;const i=(null!=e?e:"*").split("").map(e=>/\s/.test(e)&&!n?"":('"'===e&&(n=!n),e)).join("");return this.url.searchParams.set("select",i),s&&(this.headers.Prefer=`count=${s}`),new Ks.default({method:r,url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}insert(e,{count:t,defaultToNull:s=!0}={}){const r=[];if(this.headers.Prefer&&r.push(this.headers.Prefer),t&&r.push(`count=${t}`),s||r.push("missing=default"),this.headers.Prefer=r.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new Ks.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}upsert(e,{onConflict:t,ignoreDuplicates:s=!1,count:r,defaultToNull:n=!0}={}){const i=[`resolution=${s?"ignore":"merge"}-duplicates`];if(void 0!==t&&this.url.searchParams.set("on_conflict",t),this.headers.Prefer&&i.push(this.headers.Prefer),r&&i.push(`count=${r}`),n||i.push("missing=default"),this.headers.Prefer=i.join(","),Array.isArray(e)){const t=e.reduce((e,t)=>e.concat(Object.keys(t)),[]);if(t.length>0){const e=[...new Set(t)].map(e=>`"${e}"`);this.url.searchParams.set("columns",e.join(","))}}return new Ks.default({method:"POST",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}update(e,{count:t}={}){const s=[];return this.headers.Prefer&&s.push(this.headers.Prefer),t&&s.push(`count=${t}`),this.headers.Prefer=s.join(","),new Ks.default({method:"PATCH",url:this.url,headers:this.headers,schema:this.schema,body:e,fetch:this.fetch,allowEmpty:!1})}delete({count:e}={}){const t=[];return e&&t.push(`count=${e}`),this.headers.Prefer&&t.unshift(this.headers.Prefer),this.headers.Prefer=t.join(","),new Ks.default({method:"DELETE",url:this.url,headers:this.headers,schema:this.schema,fetch:this.fetch,allowEmpty:!1})}};var Hs={},Qs={};Object.defineProperty(Qs,"__esModule",{value:!0}),Qs.version=void 0,Qs.version="0.0.0-automated",Object.defineProperty(Hs,"__esModule",{value:!0}),Hs.DEFAULT_HEADERS=void 0;const Ys=Qs;Hs.DEFAULT_HEADERS={"X-Client-Info":`postgrest-js/${Ys.version}`};var Zs=_s&&_s.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(Ss,"__esModule",{value:!0});const Xs=Zs(Es),er=Zs(Ts),tr=Hs;Ss.default=class e{constructor(e,{headers:t={},schema:s,fetch:r}={}){this.url=e,this.headers=Object.assign(Object.assign({},tr.DEFAULT_HEADERS),t),this.schemaName=s,this.fetch=r}from(e){const t=new URL(`${this.url}/${e}`);return new Xs.default(t,{headers:Object.assign({},this.headers),schema:this.schemaName,fetch:this.fetch})}schema(t){return new e(this.url,{headers:this.headers,schema:t,fetch:this.fetch})}rpc(e,t={},{head:s=!1,get:r=!1,count:n}={}){let i;const a=new URL(`${this.url}/rpc/${e}`);let o;s||r?(i=s?"HEAD":"GET",Object.entries(t).filter(([e,t])=>void 0!==t).map(([e,t])=>[e,Array.isArray(t)?`{${t.join(",")}}`:`${t}`]).forEach(([e,t])=>{a.searchParams.append(e,t)})):(i="POST",o=t);const l=Object.assign({},this.headers);return n&&(l.Prefer=`count=${n}`),new er.default({method:i,url:a,headers:l,schema:this.schemaName,body:o,fetch:this.fetch,allowEmpty:!1})}};var sr=_s&&_s.__importDefault||function(e){return e&&e.__esModule?e:{default:e}};Object.defineProperty(ks,"__esModule",{value:!0}),ks.PostgrestError=ks.PostgrestBuilder=ks.PostgrestTransformBuilder=ks.PostgrestFilterBuilder=ks.PostgrestQueryBuilder=ks.PostgrestClient=void 0;const rr=sr(Ss);ks.PostgrestClient=rr.default;const nr=sr(Es);ks.PostgrestQueryBuilder=nr.default;const ir=sr(Ts);ks.PostgrestFilterBuilder=ir.default;const ar=sr(js);ks.PostgrestTransformBuilder=ar.default;const or=sr(Os);ks.PostgrestBuilder=or.default;const lr=sr(Us);ks.PostgrestError=lr.default;var cr=ks.default={PostgrestClient:rr.default,PostgrestQueryBuilder:nr.default,PostgrestFilterBuilder:ir.default,PostgrestTransformBuilder:ar.default,PostgrestBuilder:or.default,PostgrestError:lr.default};const{PostgrestClient:ur,PostgrestQueryBuilder:dr,PostgrestFilterBuilder:hr,PostgrestTransformBuilder:fr,PostgrestBuilder:pr,PostgrestError:mr}=cr;const vr=function(){if("undefined"!=typeof WebSocket)return WebSocket;if(void 0!==global.WebSocket)return global.WebSocket;if(void 0!==window.WebSocket)return window.WebSocket;if(void 0!==self.WebSocket)return self.WebSocket;throw new Error("`WebSocket` is not supported in this environment")}();var gr,yr,br,_r,wr,kr,Sr,Er,Tr,jr,Or;(yr=gr||(gr={}))[yr.connecting=0]="connecting",yr[yr.open=1]="open",yr[yr.closing=2]="closing",yr[yr.closed=3]="closed",(_r=br||(br={})).closed="closed",_r.errored="errored",_r.joined="joined",_r.joining="joining",_r.leaving="leaving",(kr=wr||(wr={})).close="phx_close",kr.error="phx_error",kr.join="phx_join",kr.reply="phx_reply",kr.leave="phx_leave",kr.access_token="access_token",(Sr||(Sr={})).websocket="websocket",(Tr=Er||(Er={})).Connecting="connecting",Tr.Open="open",Tr.Closing="closing",Tr.Closed="closed";class Cr{constructor(){this.HEADER_LENGTH=1}decode(e,t){return e.constructor===ArrayBuffer?t(this._binaryDecode(e)):t("string"==typeof e?JSON.parse(e):{})}_binaryDecode(e){const t=new DataView(e),s=new TextDecoder;return this._decodeBroadcast(e,t,s)}_decodeBroadcast(e,t,s){const r=t.getUint8(1),n=t.getUint8(2);let i=this.HEADER_LENGTH+2;const a=s.decode(e.slice(i,i+r));i+=r;const o=s.decode(e.slice(i,i+n));i+=n;return{ref:null,topic:a,event:o,payload:JSON.parse(s.decode(e.slice(i,e.byteLength)))}}}class Pr{constructor(e,t){this.callback=e,this.timerCalc=t,this.timer=void 0,this.tries=0,this.callback=e,this.timerCalc=t}reset(){this.tries=0,clearTimeout(this.timer)}scheduleTimeout(){clearTimeout(this.timer),this.timer=setTimeout(()=>{this.tries=this.tries+1,this.callback()},this.timerCalc(this.tries+1))}}(Or=jr||(jr={})).abstime="abstime",Or.bool="bool",Or.date="date",Or.daterange="daterange",Or.float4="float4",Or.float8="float8",Or.int2="int2",Or.int4="int4",Or.int4range="int4range",Or.int8="int8",Or.int8range="int8range",Or.json="json",Or.jsonb="jsonb",Or.money="money",Or.numeric="numeric",Or.oid="oid",Or.reltime="reltime",Or.text="text",Or.time="time",Or.timestamp="timestamp",Or.timestamptz="timestamptz",Or.timetz="timetz",Or.tsrange="tsrange",Or.tstzrange="tstzrange";const Ar=(e,t,s={})=>{var r;const n=null!==(r=s.skipTypes)&&void 0!==r?r:[];return Object.keys(t).reduce((s,r)=>(s[r]=xr(r,e,t,n),s),{})},xr=(e,t,s,r)=>{const n=t.find(t=>t.name===e),i=null==n?void 0:n.type,a=s[e];return i&&!r.includes(i)?Ir(i,a):Rr(a)},Ir=(e,t)=>{if("_"===e.charAt(0)){const s=e.slice(1,e.length);return Dr(t,s)}switch(e){case jr.bool:return $r(t);case jr.float4:case jr.float8:case jr.int2:case jr.int4:case jr.int8:case jr.numeric:case jr.oid:return Lr(t);case jr.json:case jr.jsonb:return Ur(t);case jr.timestamp:return qr(t);case jr.abstime:case jr.date:case jr.daterange:case jr.int4range:case jr.int8range:case jr.money:case jr.reltime:case jr.text:case jr.time:case jr.timestamptz:case jr.timetz:case jr.tsrange:case jr.tstzrange:default:return Rr(t)}},Rr=e=>e,$r=e=>{switch(e){case"t":return!0;case"f":return!1;default:return e}},Lr=e=>{if("string"==typeof e){const t=parseFloat(e);if(!Number.isNaN(t))return t}return e},Ur=e=>{if("string"==typeof e)try{return JSON.parse(e)}catch(t){return console.log(`JSON parse error: ${t}`),e}return e},Dr=(e,t)=>{if("string"!=typeof e)return e;const s=e.length-1,r=e[s];if("{"===e[0]&&"}"===r){let r;const i=e.slice(1,s);try{r=JSON.parse("["+i+"]")}catch(n){r=i?i.split(","):[]}return r.map(e=>Ir(t,e))}return e},qr=e=>"string"==typeof e?e.replace(" ","T"):e,Nr=e=>{let t=e;return t=t.replace(/^ws/i,"http"),t=t.replace(/(\/socket\/websocket|\/socket|\/websocket)\/?$/i,""),t.replace(/\/+$/,"")};class Mr{constructor(e,t,s={},r=1e4){this.channel=e,this.event=t,this.payload=s,this.timeout=r,this.sent=!1,this.timeoutTimer=void 0,this.ref="",this.receivedResp=null,this.recHooks=[],this.refEvent=null}resend(e){this.timeout=e,this._cancelRefEvent(),this.ref="",this.refEvent=null,this.receivedResp=null,this.sent=!1,this.send()}send(){this._hasReceived("timeout")||(this.startTimeout(),this.sent=!0,this.channel.socket.push({topic:this.channel.topic,event:this.event,payload:this.payload,ref:this.ref,join_ref:this.channel._joinRef()}))}updatePayload(e){this.payload=Object.assign(Object.assign({},this.payload),e)}receive(e,t){var s;return this._hasReceived(e)&&t(null===(s=this.receivedResp)||void 0===s?void 0:s.response),this.recHooks.push({status:e,callback:t}),this}startTimeout(){if(this.timeoutTimer)return;this.ref=this.channel.socket._makeRef(),this.refEvent=this.channel._replyEventName(this.ref);this.channel._on(this.refEvent,{},e=>{this._cancelRefEvent(),this._cancelTimeout(),this.receivedResp=e,this._matchReceive(e)}),this.timeoutTimer=setTimeout(()=>{this.trigger("timeout",{})},this.timeout)}trigger(e,t){this.refEvent&&this.channel._trigger(this.refEvent,{status:e,response:t})}destroy(){this._cancelRefEvent(),this._cancelTimeout()}_cancelRefEvent(){this.refEvent&&this.channel._off(this.refEvent,{})}_cancelTimeout(){clearTimeout(this.timeoutTimer),this.timeoutTimer=void 0}_matchReceive({status:e,response:t}){this.recHooks.filter(t=>t.status===e).forEach(e=>e.callback(t))}_hasReceived(e){return this.receivedResp&&this.receivedResp.status===e}}var Vr,Br,Fr,zr,Wr,Jr,Gr,Kr;(Br=Vr||(Vr={})).SYNC="sync",Br.JOIN="join",Br.LEAVE="leave";class Hr{constructor(e,t){this.channel=e,this.state={},this.pendingDiffs=[],this.joinRef=null,this.caller={onJoin:()=>{},onLeave:()=>{},onSync:()=>{}};const s=(null==t?void 0:t.events)||{state:"presence_state",diff:"presence_diff"};this.channel._on(s.state,{},e=>{const{onJoin:t,onLeave:s,onSync:r}=this.caller;this.joinRef=this.channel._joinRef(),this.state=Hr.syncState(this.state,e,t,s),this.pendingDiffs.forEach(e=>{this.state=Hr.syncDiff(this.state,e,t,s)}),this.pendingDiffs=[],r()}),this.channel._on(s.diff,{},e=>{const{onJoin:t,onLeave:s,onSync:r}=this.caller;this.inPendingSyncState()?this.pendingDiffs.push(e):(this.state=Hr.syncDiff(this.state,e,t,s),r())}),this.onJoin((e,t,s)=>{this.channel._trigger("presence",{event:"join",key:e,currentPresences:t,newPresences:s})}),this.onLeave((e,t,s)=>{this.channel._trigger("presence",{event:"leave",key:e,currentPresences:t,leftPresences:s})}),this.onSync(()=>{this.channel._trigger("presence",{event:"sync"})})}static syncState(e,t,s,r){const n=this.cloneDeep(e),i=this.transformState(t),a={},o={};return this.map(n,(e,t)=>{i[e]||(o[e]=t)}),this.map(i,(e,t)=>{const s=n[e];if(s){const r=t.map(e=>e.presence_ref),n=s.map(e=>e.presence_ref),i=t.filter(e=>n.indexOf(e.presence_ref)<0),l=s.filter(e=>r.indexOf(e.presence_ref)<0);i.length>0&&(a[e]=i),l.length>0&&(o[e]=l)}else a[e]=t}),this.syncDiff(n,{joins:a,leaves:o},s,r)}static syncDiff(e,t,s,r){const{joins:n,leaves:i}={joins:this.transformState(t.joins),leaves:this.transformState(t.leaves)};return s||(s=()=>{}),r||(r=()=>{}),this.map(n,(t,r)=>{var n;const i=null!==(n=e[t])&&void 0!==n?n:[];if(e[t]=this.cloneDeep(r),i.length>0){const s=e[t].map(e=>e.presence_ref),r=i.filter(e=>s.indexOf(e.presence_ref)<0);e[t].unshift(...r)}s(t,i,r)}),this.map(i,(t,s)=>{let n=e[t];if(!n)return;const i=s.map(e=>e.presence_ref);n=n.filter(e=>i.indexOf(e.presence_ref)<0),e[t]=n,r(t,n,s),0===n.length&&delete e[t]}),e}static map(e,t){return Object.getOwnPropertyNames(e).map(s=>t(s,e[s]))}static transformState(e){return e=this.cloneDeep(e),Object.getOwnPropertyNames(e).reduce((t,s)=>{const r=e[s];return t[s]="metas"in r?r.metas.map(e=>(e.presence_ref=e.phx_ref,delete e.phx_ref,delete e.phx_ref_prev,e)):r,t},{})}static cloneDeep(e){return JSON.parse(JSON.stringify(e))}onJoin(e){this.caller.onJoin=e}onLeave(e){this.caller.onLeave=e}onSync(e){this.caller.onSync=e}inPendingSyncState(){return!this.joinRef||this.joinRef!==this.channel._joinRef()}}(zr=Fr||(Fr={})).ALL="*",zr.INSERT="INSERT",zr.UPDATE="UPDATE",zr.DELETE="DELETE",(Jr=Wr||(Wr={})).BROADCAST="broadcast",Jr.PRESENCE="presence",Jr.POSTGRES_CHANGES="postgres_changes",Jr.SYSTEM="system",(Kr=Gr||(Gr={})).SUBSCRIBED="SUBSCRIBED",Kr.TIMED_OUT="TIMED_OUT",Kr.CLOSED="CLOSED",Kr.CHANNEL_ERROR="CHANNEL_ERROR";class Qr{constructor(e,t={config:{}},s){this.topic=e,this.params=t,this.socket=s,this.bindings={},this.state=br.closed,this.joinedOnce=!1,this.pushBuffer=[],this.subTopic=e.replace(/^realtime:/i,""),this.params.config=Object.assign({broadcast:{ack:!1,self:!1},presence:{key:""},private:!1},t.config),this.timeout=this.socket.timeout,this.joinPush=new Mr(this,wr.join,this.params,this.timeout),this.rejoinTimer=new Pr(()=>this._rejoinUntilConnected(),this.socket.reconnectAfterMs),this.joinPush.receive("ok",()=>{this.state=br.joined,this.rejoinTimer.reset(),this.pushBuffer.forEach(e=>e.send()),this.pushBuffer=[]}),this._onClose(()=>{this.rejoinTimer.reset(),this.socket.log("channel",`close ${this.topic} ${this._joinRef()}`),this.state=br.closed,this.socket._remove(this)}),this._onError(e=>{this._isLeaving()||this._isClosed()||(this.socket.log("channel",`error ${this.topic}`,e),this.state=br.errored,this.rejoinTimer.scheduleTimeout())}),this.joinPush.receive("timeout",()=>{this._isJoining()&&(this.socket.log("channel",`timeout ${this.topic}`,this.joinPush.timeout),this.state=br.errored,this.rejoinTimer.scheduleTimeout())}),this._on(wr.reply,{},(e,t)=>{this._trigger(this._replyEventName(t),e)}),this.presence=new Hr(this),this.broadcastEndpointURL=Nr(this.socket.endPoint)+"/api/broadcast",this.private=this.params.config.private||!1}subscribe(e,t=this.timeout){var s,r;if(this.socket.isConnected()||this.socket.connect(),this.state==br.closed){const{config:{broadcast:n,presence:i,private:a}}=this.params;this._onError(t=>null==e?void 0:e(Gr.CHANNEL_ERROR,t)),this._onClose(()=>null==e?void 0:e(Gr.CLOSED));const o={},l={broadcast:n,presence:i,postgres_changes:null!==(r=null===(s=this.bindings.postgres_changes)||void 0===s?void 0:s.map(e=>e.filter))&&void 0!==r?r:[],private:a};this.socket.accessTokenValue&&(o.access_token=this.socket.accessTokenValue),this.updateJoinPayload(Object.assign({config:l},o)),this.joinedOnce=!0,this._rejoin(t),this.joinPush.receive("ok",async({postgres_changes:t})=>{var s;if(this.socket.setAuth(),void 0!==t){const r=this.bindings.postgres_changes,n=null!==(s=null==r?void 0:r.length)&&void 0!==s?s:0,i=[];for(let s=0;s<n;s++){const n=r[s],{filter:{event:a,schema:o,table:l,filter:c}}=n,u=t&&t[s];if(!u||u.event!==a||u.schema!==o||u.table!==l||u.filter!==c)return this.unsubscribe(),this.state=br.errored,void(null==e||e(Gr.CHANNEL_ERROR,new Error("mismatch between server and client bindings for postgres changes")));i.push(Object.assign(Object.assign({},n),{id:u.id}))}return this.bindings.postgres_changes=i,void(e&&e(Gr.SUBSCRIBED))}null==e||e(Gr.SUBSCRIBED)}).receive("error",t=>{this.state=br.errored,null==e||e(Gr.CHANNEL_ERROR,new Error(JSON.stringify(Object.values(t).join(", ")||"error")))}).receive("timeout",()=>{null==e||e(Gr.TIMED_OUT)})}return this}presenceState(){return this.presence.state}async track(e,t={}){return await this.send({type:"presence",event:"track",payload:e},t.timeout||this.timeout)}async untrack(e={}){return await this.send({type:"presence",event:"untrack"},e)}on(e,t,s){return this._on(e,t,s)}async send(e,t={}){var s,r;if(this._canPush()||"broadcast"!==e.type)return new Promise(s=>{var r,n,i;const a=this._push(e.type,e,t.timeout||this.timeout);"broadcast"!==e.type||(null===(i=null===(n=null===(r=this.params)||void 0===r?void 0:r.config)||void 0===n?void 0:n.broadcast)||void 0===i?void 0:i.ack)||s("ok"),a.receive("ok",()=>s("ok")),a.receive("error",()=>s("error")),a.receive("timeout",()=>s("timed out"))});{const{event:i,payload:a}=e,o={method:"POST",headers:{Authorization:this.socket.accessTokenValue?`Bearer ${this.socket.accessTokenValue}`:"",apikey:this.socket.apiKey?this.socket.apiKey:"","Content-Type":"application/json"},body:JSON.stringify({messages:[{topic:this.subTopic,event:i,payload:a,private:this.private}]})};try{const e=await this._fetchWithTimeout(this.broadcastEndpointURL,o,null!==(s=t.timeout)&&void 0!==s?s:this.timeout);return await(null===(r=e.body)||void 0===r?void 0:r.cancel()),e.ok?"ok":"error"}catch(n){return"AbortError"===n.name?"timed out":"error"}}}updateJoinPayload(e){this.joinPush.updatePayload(e)}unsubscribe(e=this.timeout){this.state=br.leaving;const t=()=>{this.socket.log("channel",`leave ${this.topic}`),this._trigger(wr.close,"leave",this._joinRef())};this.joinPush.destroy();let s=null;return new Promise(r=>{s=new Mr(this,wr.leave,{},e),s.receive("ok",()=>{t(),r("ok")}).receive("timeout",()=>{t(),r("timed out")}).receive("error",()=>{r("error")}),s.send(),this._canPush()||s.trigger("ok",{})}).finally(()=>{null==s||s.destroy()})}teardown(){this.pushBuffer.forEach(e=>e.destroy()),this.rejoinTimer&&clearTimeout(this.rejoinTimer.timer),this.joinPush.destroy()}async _fetchWithTimeout(e,t,s){const r=new AbortController,n=setTimeout(()=>r.abort(),s),i=await this.socket.fetch(e,Object.assign(Object.assign({},t),{signal:r.signal}));return clearTimeout(n),i}_push(e,t,s=this.timeout){if(!this.joinedOnce)throw`tried to push '${e}' to '${this.topic}' before joining. Use channel.subscribe() before pushing events`;let r=new Mr(this,e,t,s);return this._canPush()?r.send():(r.startTimeout(),this.pushBuffer.push(r)),r}_onMessage(e,t,s){return t}_isMember(e){return this.topic===e}_joinRef(){return this.joinPush.ref}_trigger(e,t,s){var r,n;const i=e.toLocaleLowerCase(),{close:a,error:o,leave:l,join:c}=wr;if(s&&[a,o,l,c].indexOf(i)>=0&&s!==this._joinRef())return;let u=this._onMessage(i,t,s);if(t&&!u)throw"channel onMessage callbacks must return the payload, modified or unmodified";["insert","update","delete"].includes(i)?null===(r=this.bindings.postgres_changes)||void 0===r||r.filter(e=>{var t,s,r;return"*"===(null===(t=e.filter)||void 0===t?void 0:t.event)||(null===(r=null===(s=e.filter)||void 0===s?void 0:s.event)||void 0===r?void 0:r.toLocaleLowerCase())===i}).map(e=>e.callback(u,s)):null===(n=this.bindings[i])||void 0===n||n.filter(e=>{var s,r,n,a,o,l;if(["broadcast","presence","postgres_changes"].includes(i)){if("id"in e){const i=e.id,a=null===(s=e.filter)||void 0===s?void 0:s.event;return i&&(null===(r=t.ids)||void 0===r?void 0:r.includes(i))&&("*"===a||(null==a?void 0:a.toLocaleLowerCase())===(null===(n=t.data)||void 0===n?void 0:n.type.toLocaleLowerCase()))}{const s=null===(o=null===(a=null==e?void 0:e.filter)||void 0===a?void 0:a.event)||void 0===o?void 0:o.toLocaleLowerCase();return"*"===s||s===(null===(l=null==t?void 0:t.event)||void 0===l?void 0:l.toLocaleLowerCase())}}return e.type.toLocaleLowerCase()===i}).map(e=>{if("object"==typeof u&&"ids"in u){const e=u.data,{schema:t,table:s,commit_timestamp:r,type:n,errors:i}=e,a={schema:t,table:s,commit_timestamp:r,eventType:n,new:{},old:{},errors:i};u=Object.assign(Object.assign({},a),this._getPayloadRecords(e))}e.callback(u,s)})}_isClosed(){return this.state===br.closed}_isJoined(){return this.state===br.joined}_isJoining(){return this.state===br.joining}_isLeaving(){return this.state===br.leaving}_replyEventName(e){return`chan_reply_${e}`}_on(e,t,s){const r=e.toLocaleLowerCase(),n={type:r,filter:t,callback:s};return this.bindings[r]?this.bindings[r].push(n):this.bindings[r]=[n],this}_off(e,t){const s=e.toLocaleLowerCase();return this.bindings[s]=this.bindings[s].filter(e=>{var r;return!((null===(r=e.type)||void 0===r?void 0:r.toLocaleLowerCase())===s&&Qr.isEqual(e.filter,t))}),this}static isEqual(e,t){if(Object.keys(e).length!==Object.keys(t).length)return!1;for(const s in e)if(e[s]!==t[s])return!1;return!0}_rejoinUntilConnected(){this.rejoinTimer.scheduleTimeout(),this.socket.isConnected()&&this._rejoin()}_onClose(e){this._on(wr.close,{},e)}_onError(e){this._on(wr.error,{},t=>e(t))}_canPush(){return this.socket.isConnected()&&this._isJoined()}_rejoin(e=this.timeout){this._isLeaving()||(this.socket._leaveOpenTopic(this.topic),this.state=br.joining,this.joinPush.resend(e))}_getPayloadRecords(e){const t={new:{},old:{}};return"INSERT"!==e.type&&"UPDATE"!==e.type||(t.new=Ar(e.columns,e.record)),"UPDATE"!==e.type&&"DELETE"!==e.type||(t.old=Ar(e.columns,e.old_record)),t}}const Yr=()=>{};class Zr{constructor(e,t){var s;this.accessTokenValue=null,this.apiKey=null,this.channels=new Array,this.endPoint="",this.httpEndpoint="",this.headers={},this.params={},this.timeout=1e4,this.heartbeatIntervalMs=25e3,this.heartbeatTimer=void 0,this.pendingHeartbeatRef=null,this.heartbeatCallback=Yr,this.ref=0,this.logger=Yr,this.conn=null,this.sendBuffer=[],this.serializer=new Cr,this.stateChangeCallbacks={open:[],close:[],error:[],message:[]},this.accessToken=null,this._resolveFetch=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ds(async()=>{const{default:e}=await Promise.resolve().then(()=>$s);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},this.endPoint=`${e}/${Sr.websocket}`,this.httpEndpoint=Nr(e),(null==t?void 0:t.transport)?this.transport=t.transport:this.transport=null,(null==t?void 0:t.params)&&(this.params=t.params),(null==t?void 0:t.timeout)&&(this.timeout=t.timeout),(null==t?void 0:t.logger)&&(this.logger=t.logger),((null==t?void 0:t.logLevel)||(null==t?void 0:t.log_level))&&(this.logLevel=t.logLevel||t.log_level,this.params=Object.assign(Object.assign({},this.params),{log_level:this.logLevel})),(null==t?void 0:t.heartbeatIntervalMs)&&(this.heartbeatIntervalMs=t.heartbeatIntervalMs);const r=null===(s=null==t?void 0:t.params)||void 0===s?void 0:s.apikey;if(r&&(this.accessTokenValue=r,this.apiKey=r),this.reconnectAfterMs=(null==t?void 0:t.reconnectAfterMs)?t.reconnectAfterMs:e=>[1e3,2e3,5e3,1e4][e-1]||1e4,this.encode=(null==t?void 0:t.encode)?t.encode:(e,t)=>t(JSON.stringify(e)),this.decode=(null==t?void 0:t.decode)?t.decode:this.serializer.decode.bind(this.serializer),this.reconnectTimer=new Pr(async()=>{this.disconnect(),this.connect()},this.reconnectAfterMs),this.fetch=this._resolveFetch(null==t?void 0:t.fetch),null==t?void 0:t.worker){if("undefined"!=typeof window&&!window.Worker)throw new Error("Web Worker is not supported");this.worker=(null==t?void 0:t.worker)||!1,this.workerUrl=null==t?void 0:t.workerUrl}this.accessToken=(null==t?void 0:t.accessToken)||null}connect(){if(!this.conn){if(this.transport||(this.transport=vr),!this.transport)throw new Error("No transport provided");this.conn=new this.transport(this.endpointURL()),this.setupConnection()}}endpointURL(){return this._appendParams(this.endPoint,Object.assign({},this.params,{vsn:"1.0.0"}))}disconnect(e,t){this.conn&&(this.conn.onclose=function(){},e?this.conn.close(e,null!=t?t:""):this.conn.close(),this.conn=null,this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.reset(),this.channels.forEach(e=>e.teardown()))}getChannels(){return this.channels}async removeChannel(e){const t=await e.unsubscribe();return 0===this.channels.length&&this.disconnect(),t}async removeAllChannels(){const e=await Promise.all(this.channels.map(e=>e.unsubscribe()));return this.channels=[],this.disconnect(),e}log(e,t,s){this.logger(e,t,s)}connectionState(){switch(this.conn&&this.conn.readyState){case gr.connecting:return Er.Connecting;case gr.open:return Er.Open;case gr.closing:return Er.Closing;default:return Er.Closed}}isConnected(){return this.connectionState()===Er.Open}channel(e,t={config:{}}){const s=`realtime:${e}`,r=this.getChannels().find(e=>e.topic===s);if(r)return r;{const s=new Qr(`realtime:${e}`,t,this);return this.channels.push(s),s}}push(e){const{topic:t,event:s,payload:r,ref:n}=e,i=()=>{this.encode(e,e=>{var t;null===(t=this.conn)||void 0===t||t.send(e)})};this.log("push",`${t} ${s} (${n})`,r),this.isConnected()?i():this.sendBuffer.push(i)}async setAuth(e=null){let t=e||this.accessToken&&await this.accessToken()||this.accessTokenValue;this.accessTokenValue!=t&&(this.accessTokenValue=t,this.channels.forEach(e=>{const s={access_token:t,version:"realtime-js/2.11.15"};t&&e.updateJoinPayload(s),e.joinedOnce&&e._isJoined()&&e._push(wr.access_token,{access_token:t})}))}async sendHeartbeat(){var e;if(this.isConnected()){if(this.pendingHeartbeatRef)return this.pendingHeartbeatRef=null,this.log("transport","heartbeat timeout. Attempting to re-establish connection"),this.heartbeatCallback("timeout"),void(null===(e=this.conn)||void 0===e||e.close(1e3,"hearbeat timeout"));this.pendingHeartbeatRef=this._makeRef(),this.push({topic:"phoenix",event:"heartbeat",payload:{},ref:this.pendingHeartbeatRef}),this.heartbeatCallback("sent"),await this.setAuth()}else this.heartbeatCallback("disconnected")}onHeartbeat(e){this.heartbeatCallback=e}flushSendBuffer(){this.isConnected()&&this.sendBuffer.length>0&&(this.sendBuffer.forEach(e=>e()),this.sendBuffer=[])}_makeRef(){let e=this.ref+1;return e===this.ref?this.ref=0:this.ref=e,this.ref.toString()}_leaveOpenTopic(e){let t=this.channels.find(t=>t.topic===e&&(t._isJoined()||t._isJoining()));t&&(this.log("transport",`leaving duplicate topic "${e}"`),t.unsubscribe())}_remove(e){this.channels=this.channels.filter(t=>t.topic!==e.topic)}setupConnection(){this.conn&&(this.conn.binaryType="arraybuffer",this.conn.onopen=()=>this._onConnOpen(),this.conn.onerror=e=>this._onConnError(e),this.conn.onmessage=e=>this._onConnMessage(e),this.conn.onclose=e=>this._onConnClose(e))}_onConnMessage(e){this.decode(e.data,e=>{let{topic:t,event:s,payload:r,ref:n}=e;"phoenix"===t&&"phx_reply"===s&&this.heartbeatCallback("ok"==e.payload.status?"ok":"error"),n&&n===this.pendingHeartbeatRef&&(this.pendingHeartbeatRef=null),this.log("receive",`${r.status||""} ${t} ${s} ${n&&"("+n+")"||""}`,r),Array.from(this.channels).filter(e=>e._isMember(t)).forEach(e=>e._trigger(s,r,n)),this.stateChangeCallbacks.message.forEach(t=>t(e))})}_onConnOpen(){this.log("transport",`connected to ${this.endpointURL()}`),this.flushSendBuffer(),this.reconnectTimer.reset(),this.worker?this.workerRef||this._startWorkerHeartbeat():this._startHeartbeat(),this.stateChangeCallbacks.open.forEach(e=>e())}_startHeartbeat(){this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.heartbeatTimer=setInterval(()=>this.sendHeartbeat(),this.heartbeatIntervalMs)}_startWorkerHeartbeat(){this.workerUrl?this.log("worker",`starting worker for from ${this.workerUrl}`):this.log("worker","starting default worker");const e=this._workerObjectUrl(this.workerUrl);this.workerRef=new Worker(e),this.workerRef.onerror=e=>{this.log("worker","worker error",e.message),this.workerRef.terminate()},this.workerRef.onmessage=e=>{"keepAlive"===e.data.event&&this.sendHeartbeat()},this.workerRef.postMessage({event:"start",interval:this.heartbeatIntervalMs})}_onConnClose(e){this.log("transport","close",e),this._triggerChanError(),this.heartbeatTimer&&clearInterval(this.heartbeatTimer),this.reconnectTimer.scheduleTimeout(),this.stateChangeCallbacks.close.forEach(t=>t(e))}_onConnError(e){this.log("transport",`${e}`),this._triggerChanError(),this.stateChangeCallbacks.error.forEach(t=>t(e))}_triggerChanError(){this.channels.forEach(e=>e._trigger(wr.error))}_appendParams(e,t){if(0===Object.keys(t).length)return e;const s=e.match(/\?/)?"&":"?";return`${e}${s}${new URLSearchParams(t)}`}_workerObjectUrl(e){let t;if(e)t=e;else{const e=new Blob(['\n  addEventListener("message", (e) => {\n    if (e.data.event === "start") {\n      setInterval(() => postMessage({ event: "keepAlive" }), e.data.interval);\n    }\n  });'],{type:"application/javascript"});t=URL.createObjectURL(e)}return t}}class Xr extends Error{constructor(e){super(e),this.__isStorageError=!0,this.name="StorageError"}}function en(e){return"object"==typeof e&&null!==e&&"__isStorageError"in e}class tn extends Xr{constructor(e,t){super(e),this.name="StorageApiError",this.status=t}toJSON(){return{name:this.name,message:this.message,status:this.status}}}class sn extends Xr{constructor(e,t){super(e),this.name="StorageUnknownError",this.originalError=t}}var rn=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};const nn=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ds(async()=>{const{default:e}=await Promise.resolve().then(()=>$s);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},an=e=>{if(Array.isArray(e))return e.map(e=>an(e));if("function"==typeof e||e!==Object(e))return e;const t={};return Object.entries(e).forEach(([e,s])=>{const r=e.replace(/([-_][a-z])/gi,e=>e.toUpperCase().replace(/[-_]/g,""));t[r]=an(s)}),t};var on=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};const ln=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),cn=(e,t,s)=>on(void 0,void 0,void 0,function*(){const r=yield rn(void 0,void 0,void 0,function*(){return"undefined"==typeof Response?(yield ds(()=>Promise.resolve().then(()=>$s),void 0,import.meta.url)).Response:Response});e instanceof r&&!(null==s?void 0:s.noResolveJson)?e.json().then(s=>{t(new tn(ln(s),e.status||500))}).catch(e=>{t(new sn(ln(e),e))}):t(new sn(ln(e),e))});function un(e,t,s,r,n,i){return on(this,void 0,void 0,function*(){return new Promise((a,o)=>{e(s,((e,t,s,r)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json"},null==t?void 0:t.headers),r&&(n.body=JSON.stringify(r)),Object.assign(Object.assign({},n),s))})(t,r,n,i)).then(e=>{if(!e.ok)throw e;return(null==r?void 0:r.noResolveJson)?e:e.json()}).then(e=>a(e)).catch(e=>cn(e,o,r))})})}function dn(e,t,s,r){return on(this,void 0,void 0,function*(){return un(e,"GET",t,s,r)})}function hn(e,t,s,r,n){return on(this,void 0,void 0,function*(){return un(e,"POST",t,r,n,s)})}function fn(e,t,s,r,n){return on(this,void 0,void 0,function*(){return un(e,"DELETE",t,r,n,s)})}var pn=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};const mn={limit:100,offset:0,sortBy:{column:"name",order:"asc"}},vn={cacheControl:"3600",contentType:"text/plain;charset=UTF-8",upsert:!1};class gn{constructor(e,t={},s,r){this.url=e,this.headers=t,this.bucketId=s,this.fetch=nn(r)}uploadOrUpdate(e,t,s,r){return pn(this,void 0,void 0,function*(){try{let n;const i=Object.assign(Object.assign({},vn),r);let a=Object.assign(Object.assign({},this.headers),"POST"===e&&{"x-upsert":String(i.upsert)});const o=i.metadata;"undefined"!=typeof Blob&&s instanceof Blob?(n=new FormData,n.append("cacheControl",i.cacheControl),o&&n.append("metadata",this.encodeMetadata(o)),n.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(n=s,n.append("cacheControl",i.cacheControl),o&&n.append("metadata",this.encodeMetadata(o))):(n=s,a["cache-control"]=`max-age=${i.cacheControl}`,a["content-type"]=i.contentType,o&&(a["x-metadata"]=this.toBase64(this.encodeMetadata(o)))),(null==r?void 0:r.headers)&&(a=Object.assign(Object.assign({},a),r.headers));const l=this._removeEmptyFolders(t),c=this._getFinalPath(l),u=yield this.fetch(`${this.url}/object/${c}`,Object.assign({method:e,body:n,headers:a},(null==i?void 0:i.duplex)?{duplex:i.duplex}:{})),d=yield u.json();if(u.ok)return{data:{path:l,id:d.Id,fullPath:d.Key},error:null};return{data:null,error:d}}catch(n){if(en(n))return{data:null,error:n};throw n}})}upload(e,t,s){return pn(this,void 0,void 0,function*(){return this.uploadOrUpdate("POST",e,t,s)})}uploadToSignedUrl(e,t,s,r){return pn(this,void 0,void 0,function*(){const n=this._removeEmptyFolders(e),i=this._getFinalPath(n),a=new URL(this.url+`/object/upload/sign/${i}`);a.searchParams.set("token",t);try{let e;const t=Object.assign({upsert:vn.upsert},r),i=Object.assign(Object.assign({},this.headers),{"x-upsert":String(t.upsert)});"undefined"!=typeof Blob&&s instanceof Blob?(e=new FormData,e.append("cacheControl",t.cacheControl),e.append("",s)):"undefined"!=typeof FormData&&s instanceof FormData?(e=s,e.append("cacheControl",t.cacheControl)):(e=s,i["cache-control"]=`max-age=${t.cacheControl}`,i["content-type"]=t.contentType);const o=yield this.fetch(a.toString(),{method:"PUT",body:e,headers:i}),l=yield o.json();if(o.ok)return{data:{path:n,fullPath:l.Key},error:null};return{data:null,error:l}}catch(o){if(en(o))return{data:null,error:o};throw o}})}createSignedUploadUrl(e,t){return pn(this,void 0,void 0,function*(){try{let s=this._getFinalPath(e);const r=Object.assign({},this.headers);(null==t?void 0:t.upsert)&&(r["x-upsert"]="true");const n=yield hn(this.fetch,`${this.url}/object/upload/sign/${s}`,{},{headers:r}),i=new URL(this.url+n.url),a=i.searchParams.get("token");if(!a)throw new Xr("No token returned by API");return{data:{signedUrl:i.toString(),path:e,token:a},error:null}}catch(s){if(en(s))return{data:null,error:s};throw s}})}update(e,t,s){return pn(this,void 0,void 0,function*(){return this.uploadOrUpdate("PUT",e,t,s)})}move(e,t,s){return pn(this,void 0,void 0,function*(){try{return{data:yield hn(this.fetch,`${this.url}/object/move`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers}),error:null}}catch(r){if(en(r))return{data:null,error:r};throw r}})}copy(e,t,s){return pn(this,void 0,void 0,function*(){try{return{data:{path:(yield hn(this.fetch,`${this.url}/object/copy`,{bucketId:this.bucketId,sourceKey:e,destinationKey:t,destinationBucket:null==s?void 0:s.destinationBucket},{headers:this.headers})).Key},error:null}}catch(r){if(en(r))return{data:null,error:r};throw r}})}createSignedUrl(e,t,s){return pn(this,void 0,void 0,function*(){try{let r=this._getFinalPath(e),n=yield hn(this.fetch,`${this.url}/object/sign/${r}`,Object.assign({expiresIn:t},(null==s?void 0:s.transform)?{transform:s.transform}:{}),{headers:this.headers});const i=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return n={signedUrl:encodeURI(`${this.url}${n.signedURL}${i}`)},{data:n,error:null}}catch(r){if(en(r))return{data:null,error:r};throw r}})}createSignedUrls(e,t,s){return pn(this,void 0,void 0,function*(){try{const r=yield hn(this.fetch,`${this.url}/object/sign/${this.bucketId}`,{expiresIn:t,paths:e},{headers:this.headers}),n=(null==s?void 0:s.download)?`&download=${!0===s.download?"":s.download}`:"";return{data:r.map(e=>Object.assign(Object.assign({},e),{signedUrl:e.signedURL?encodeURI(`${this.url}${e.signedURL}${n}`):null})),error:null}}catch(r){if(en(r))return{data:null,error:r};throw r}})}download(e,t){return pn(this,void 0,void 0,function*(){const s=void 0!==(null==t?void 0:t.transform)?"render/image/authenticated":"object",r=this.transformOptsToQueryString((null==t?void 0:t.transform)||{}),n=r?`?${r}`:"";try{const t=this._getFinalPath(e),r=yield dn(this.fetch,`${this.url}/${s}/${t}${n}`,{headers:this.headers,noResolveJson:!0});return{data:yield r.blob(),error:null}}catch(i){if(en(i))return{data:null,error:i};throw i}})}info(e){return pn(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{const e=yield dn(this.fetch,`${this.url}/object/info/${t}`,{headers:this.headers});return{data:an(e),error:null}}catch(s){if(en(s))return{data:null,error:s};throw s}})}exists(e){return pn(this,void 0,void 0,function*(){const t=this._getFinalPath(e);try{return yield function(e,t,s,r){return on(this,void 0,void 0,function*(){return un(e,"HEAD",t,Object.assign(Object.assign({},s),{noResolveJson:!0}),r)})}(this.fetch,`${this.url}/object/${t}`,{headers:this.headers}),{data:!0,error:null}}catch(s){if(en(s)&&s instanceof sn){const e=s.originalError;if([400,404].includes(null==e?void 0:e.status))return{data:!1,error:s}}throw s}})}getPublicUrl(e,t){const s=this._getFinalPath(e),r=[],n=(null==t?void 0:t.download)?`download=${!0===t.download?"":t.download}`:"";""!==n&&r.push(n);const i=void 0!==(null==t?void 0:t.transform)?"render/image":"object",a=this.transformOptsToQueryString((null==t?void 0:t.transform)||{});""!==a&&r.push(a);let o=r.join("&");return""!==o&&(o=`?${o}`),{data:{publicUrl:encodeURI(`${this.url}/${i}/public/${s}${o}`)}}}remove(e){return pn(this,void 0,void 0,function*(){try{return{data:yield fn(this.fetch,`${this.url}/object/${this.bucketId}`,{prefixes:e},{headers:this.headers}),error:null}}catch(t){if(en(t))return{data:null,error:t};throw t}})}list(e,t,s){return pn(this,void 0,void 0,function*(){try{const r=Object.assign(Object.assign(Object.assign({},mn),t),{prefix:e||""});return{data:yield hn(this.fetch,`${this.url}/object/list/${this.bucketId}`,r,{headers:this.headers},s),error:null}}catch(r){if(en(r))return{data:null,error:r};throw r}})}encodeMetadata(e){return JSON.stringify(e)}toBase64(e){return"undefined"!=typeof Buffer?Buffer.from(e).toString("base64"):btoa(e)}_getFinalPath(e){return`${this.bucketId}/${e}`}_removeEmptyFolders(e){return e.replace(/^\/|\/$/g,"").replace(/\/+/g,"/")}transformOptsToQueryString(e){const t=[];return e.width&&t.push(`width=${e.width}`),e.height&&t.push(`height=${e.height}`),e.resize&&t.push(`resize=${e.resize}`),e.format&&t.push(`format=${e.format}`),e.quality&&t.push(`quality=${e.quality}`),t.join("&")}}const yn={"X-Client-Info":"storage-js/2.7.1"};var bn=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};class _n{constructor(e,t={},s){this.url=e,this.headers=Object.assign(Object.assign({},yn),t),this.fetch=nn(s)}listBuckets(){return bn(this,void 0,void 0,function*(){try{return{data:yield dn(this.fetch,`${this.url}/bucket`,{headers:this.headers}),error:null}}catch(e){if(en(e))return{data:null,error:e};throw e}})}getBucket(e){return bn(this,void 0,void 0,function*(){try{return{data:yield dn(this.fetch,`${this.url}/bucket/${e}`,{headers:this.headers}),error:null}}catch(t){if(en(t))return{data:null,error:t};throw t}})}createBucket(e,t={public:!1}){return bn(this,void 0,void 0,function*(){try{return{data:yield hn(this.fetch,`${this.url}/bucket`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers}),error:null}}catch(s){if(en(s))return{data:null,error:s};throw s}})}updateBucket(e,t){return bn(this,void 0,void 0,function*(){try{const s=yield function(e,t,s,r,n){return on(this,void 0,void 0,function*(){return un(e,"PUT",t,r,n,s)})}(this.fetch,`${this.url}/bucket/${e}`,{id:e,name:e,public:t.public,file_size_limit:t.fileSizeLimit,allowed_mime_types:t.allowedMimeTypes},{headers:this.headers});return{data:s,error:null}}catch(s){if(en(s))return{data:null,error:s};throw s}})}emptyBucket(e){return bn(this,void 0,void 0,function*(){try{return{data:yield hn(this.fetch,`${this.url}/bucket/${e}/empty`,{},{headers:this.headers}),error:null}}catch(t){if(en(t))return{data:null,error:t};throw t}})}deleteBucket(e){return bn(this,void 0,void 0,function*(){try{return{data:yield fn(this.fetch,`${this.url}/bucket/${e}`,{},{headers:this.headers}),error:null}}catch(t){if(en(t))return{data:null,error:t};throw t}})}}class wn extends _n{constructor(e,t={},s){super(e,t,s)}from(e){return new gn(this.url,this.headers,e,this.fetch)}}let kn="";kn="undefined"!=typeof Deno?"deno":"undefined"!=typeof document?"web":"undefined"!=typeof navigator&&"ReactNative"===navigator.product?"react-native":"node";const Sn={headers:{"X-Client-Info":`supabase-js-${kn}/2.50.2`}},En={schema:"public"},Tn={autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,flowType:"implicit"},jn={};var On=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};const Cn=(e,t,s)=>{const r=(e=>{let t;return t=e||("undefined"==typeof fetch?As:fetch),(...e)=>t(...e)})(s),n="undefined"==typeof Headers?xs:Headers;return(s,i)=>On(void 0,void 0,void 0,function*(){var a;const o=null!==(a=yield t())&&void 0!==a?a:e;let l=new n(null==i?void 0:i.headers);return l.has("apikey")||l.set("apikey",e),l.has("Authorization")||l.set("Authorization",`Bearer ${o}`),r(s,Object.assign(Object.assign({},i),{headers:l}))})};var Pn=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};const An="2.70.0",xn=3e4,In=9e4,Rn={"X-Client-Info":`gotrue-js/${An}`},$n="X-Supabase-Api-Version",Ln={timestamp:Date.parse("2024-01-01T00:00:00.0Z"),name:"2024-01-01"},Un=/^([a-z0-9_-]{4})*($|[a-z0-9_-]{3}$|[a-z0-9_-]{2}$)$/i;class Dn extends Error{constructor(e,t,s){super(e),this.__isAuthError=!0,this.name="AuthError",this.status=t,this.code=s}}function qn(e){return"object"==typeof e&&null!==e&&"__isAuthError"in e}class Nn extends Dn{constructor(e,t,s){super(e,t,s),this.name="AuthApiError",this.status=t,this.code=s}}class Mn extends Dn{constructor(e,t){super(e),this.name="AuthUnknownError",this.originalError=t}}class Vn extends Dn{constructor(e,t,s,r){super(e,s,r),this.name=t,this.status=s}}class Bn extends Vn{constructor(){super("Auth session missing!","AuthSessionMissingError",400,void 0)}}class Fn extends Vn{constructor(){super("Auth session or user missing","AuthInvalidTokenResponseError",500,void 0)}}class zn extends Vn{constructor(e){super(e,"AuthInvalidCredentialsError",400,void 0)}}class Wn extends Vn{constructor(e,t=null){super(e,"AuthImplicitGrantRedirectError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Jn extends Vn{constructor(e,t=null){super(e,"AuthPKCEGrantCodeExchangeError",500,void 0),this.details=null,this.details=t}toJSON(){return{name:this.name,message:this.message,status:this.status,details:this.details}}}class Gn extends Vn{constructor(e,t){super(e,"AuthRetryableFetchError",t,void 0)}}function Kn(e){return qn(e)&&"AuthRetryableFetchError"===e.name}class Hn extends Vn{constructor(e,t,s){super(e,"AuthWeakPasswordError",t,"weak_password"),this.reasons=s}}class Qn extends Vn{constructor(e){super(e,"AuthInvalidJwtError",400,"invalid_jwt")}}const Yn="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-_".split(""),Zn=" \t\n\r=".split(""),Xn=(()=>{const e=new Array(128);for(let t=0;t<e.length;t+=1)e[t]=-1;for(let t=0;t<Zn.length;t+=1)e[Zn[t].charCodeAt(0)]=-2;for(let t=0;t<Yn.length;t+=1)e[Yn[t].charCodeAt(0)]=t;return e})();function ei(e,t,s){if(null!==e)for(t.queue=t.queue<<8|e,t.queuedBits+=8;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;s(Yn[e]),t.queuedBits-=6}else if(t.queuedBits>0)for(t.queue=t.queue<<6-t.queuedBits,t.queuedBits=6;t.queuedBits>=6;){const e=t.queue>>t.queuedBits-6&63;s(Yn[e]),t.queuedBits-=6}}function ti(e,t,s){const r=Xn[e];if(!(r>-1)){if(-2===r)return;throw new Error(`Invalid Base64-URL character "${String.fromCharCode(e)}"`)}for(t.queue=t.queue<<6|r,t.queuedBits+=6;t.queuedBits>=8;)s(t.queue>>t.queuedBits-8&255),t.queuedBits-=8}function si(e){const t=[],s=e=>{t.push(String.fromCodePoint(e))},r={utf8seq:0,codepoint:0},n={queue:0,queuedBits:0},i=e=>{!function(e,t,s){if(0===t.utf8seq){if(e<=127)return void s(e);for(let s=1;s<6;s+=1)if(!(e>>7-s&1)){t.utf8seq=s;break}if(2===t.utf8seq)t.codepoint=31&e;else if(3===t.utf8seq)t.codepoint=15&e;else{if(4!==t.utf8seq)throw new Error("Invalid UTF-8 sequence");t.codepoint=7&e}t.utf8seq-=1}else if(t.utf8seq>0){if(e<=127)throw new Error("Invalid UTF-8 sequence");t.codepoint=t.codepoint<<6|63&e,t.utf8seq-=1,0===t.utf8seq&&s(t.codepoint)}}(e,r,s)};for(let a=0;a<e.length;a+=1)ti(e.charCodeAt(a),n,i);return t.join("")}function ri(e,t){if(!(e<=127)){if(e<=2047)return t(192|e>>6),void t(128|63&e);if(e<=65535)return t(224|e>>12),t(128|e>>6&63),void t(128|63&e);if(e<=1114111)return t(240|e>>18),t(128|e>>12&63),t(128|e>>6&63),void t(128|63&e);throw new Error(`Unrecognized Unicode codepoint: ${e.toString(16)}`)}t(e)}function ni(e){const t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};for(let n=0;n<e.length;n+=1)ti(e.charCodeAt(n),s,r);return new Uint8Array(t)}function ii(e){const t=[];return function(e,t){for(let s=0;s<e.length;s+=1){let r=e.charCodeAt(s);if(r>55295&&r<=56319){const t=1024*(r-55296)&65535;r=65536+(e.charCodeAt(s+1)-56320&65535|t),s+=1}ri(r,t)}}(e,e=>t.push(e)),new Uint8Array(t)}function ai(e){const t=[],s={queue:0,queuedBits:0},r=e=>{t.push(e)};return e.forEach(e=>ei(e,s,r)),ei(null,s,r),t.join("")}const oi=()=>"undefined"!=typeof window&&"undefined"!=typeof document,li={tested:!1,writable:!1},ci=()=>{if(!oi())return!1;try{if("object"!=typeof globalThis.localStorage)return!1}catch(t){return!1}if(li.tested)return li.writable;const e=`lswt-${Math.random()}${Math.random()}`;try{globalThis.localStorage.setItem(e,e),globalThis.localStorage.removeItem(e),li.tested=!0,li.writable=!0}catch(t){li.tested=!0,li.writable=!1}return li.writable};const ui=e=>{let t;return t=e||("undefined"==typeof fetch?(...e)=>ds(async()=>{const{default:e}=await Promise.resolve().then(()=>$s);return{default:e}},void 0,import.meta.url).then(({default:t})=>t(...e)):fetch),(...e)=>t(...e)},di=async(e,t,s)=>{await e.setItem(t,JSON.stringify(s))},hi=async(e,t)=>{const s=await e.getItem(t);if(!s)return null;try{return JSON.parse(s)}catch(r){return s}},fi=async(e,t)=>{await e.removeItem(t)};class pi{constructor(){this.promise=new pi.promiseConstructor((e,t)=>{this.resolve=e,this.reject=t})}}function mi(e){const t=e.split(".");if(3!==t.length)throw new Qn("Invalid JWT structure");for(let s=0;s<t.length;s++)if(!Un.test(t[s]))throw new Qn("JWT not in base64url format");return{header:JSON.parse(si(t[0])),payload:JSON.parse(si(t[1])),signature:ni(t[2]),raw:{header:t[0],payload:t[1]}}}function vi(e){return("0"+e.toString(16)).substr(-2)}async function gi(e){if(!("undefined"!=typeof crypto&&void 0!==crypto.subtle&&"undefined"!=typeof TextEncoder))return console.warn("WebCrypto API is not supported. Code challenge method will default to use plain instead of sha256."),e;const t=await async function(e){const t=(new TextEncoder).encode(e),s=await crypto.subtle.digest("SHA-256",t),r=new Uint8Array(s);return Array.from(r).map(e=>String.fromCharCode(e)).join("")}(e);return btoa(t).replace(/\+/g,"-").replace(/\//g,"_").replace(/=+$/,"")}async function yi(e,t,s=!1){const r=function(){const e=new Uint32Array(56);if("undefined"==typeof crypto){const e="ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789-._~",t=e.length;let s="";for(let r=0;r<56;r++)s+=e.charAt(Math.floor(Math.random()*t));return s}return crypto.getRandomValues(e),Array.from(e,vi).join("")}();let n=r;s&&(n+="/PASSWORD_RECOVERY"),await di(e,`${t}-code-verifier`,n);const i=await gi(r);return[i,r===i?"plain":"s256"]}pi.promiseConstructor=Promise;const bi=/^2[0-9]{3}-(0[1-9]|1[0-2])-(0[1-9]|1[0-9]|2[0-9]|3[0-1])$/i;const _i=/^[0-9a-f]{8}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{4}-[0-9a-f]{12}$/;function wi(e){if(!_i.test(e))throw new Error("@supabase/auth-js: Expected parameter to be UUID but is not")}const ki=e=>e.msg||e.message||e.error_description||e.error||JSON.stringify(e),Si=[502,503,504];async function Ei(e){var t,s;if(!("object"==typeof(s=e)&&null!==s&&"status"in s&&"ok"in s&&"json"in s&&"function"==typeof s.json))throw new Gn(ki(e),0);if(Si.includes(e.status))throw new Gn(ki(e),e.status);let r,n;try{r=await e.json()}catch(a){throw new Mn(ki(a),a)}const i=function(e){const t=e.headers.get($n);if(!t)return null;if(!t.match(bi))return null;try{return new Date(`${t}T00:00:00.0Z`)}catch(a){return null}}(e);if(i&&i.getTime()>=Ln.timestamp&&"object"==typeof r&&r&&"string"==typeof r.code?n=r.code:"object"==typeof r&&r&&"string"==typeof r.error_code&&(n=r.error_code),n){if("weak_password"===n)throw new Hn(ki(r),e.status,(null===(t=r.weak_password)||void 0===t?void 0:t.reasons)||[]);if("session_not_found"===n)throw new Bn}else if("object"==typeof r&&r&&"object"==typeof r.weak_password&&r.weak_password&&Array.isArray(r.weak_password.reasons)&&r.weak_password.reasons.length&&r.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0))throw new Hn(ki(r),e.status,r.weak_password.reasons);throw new Nn(ki(r),e.status||500,n)}async function Ti(e,t,s,r){var n;const i=Object.assign({},null==r?void 0:r.headers);i[$n]||(i[$n]=Ln.name),(null==r?void 0:r.jwt)&&(i.Authorization=`Bearer ${r.jwt}`);const a=null!==(n=null==r?void 0:r.query)&&void 0!==n?n:{};(null==r?void 0:r.redirectTo)&&(a.redirect_to=r.redirectTo);const o=Object.keys(a).length?"?"+new URLSearchParams(a).toString():"",l=await async function(e,t,s,r,n,i){const a=((e,t,s,r)=>{const n={method:e,headers:(null==t?void 0:t.headers)||{}};return"GET"===e?n:(n.headers=Object.assign({"Content-Type":"application/json;charset=UTF-8"},null==t?void 0:t.headers),n.body=JSON.stringify(r),Object.assign(Object.assign({},n),s))})(t,r,n,i);let o;try{o=await e(s,Object.assign({},a))}catch(l){throw console.error(l),new Gn(ki(l),0)}o.ok||await Ei(o);if(null==r?void 0:r.noResolveJson)return o;try{return await o.json()}catch(l){await Ei(l)}}(e,t,s+o,{headers:i,noResolveJson:null==r?void 0:r.noResolveJson},{},null==r?void 0:r.body);return(null==r?void 0:r.xform)?null==r?void 0:r.xform(l):{data:Object.assign({},l),error:null}}function ji(e){var t;let s=null;var r;(function(e){return e.access_token&&e.refresh_token&&e.expires_in})(e)&&(s=Object.assign({},e),e.expires_at||(s.expires_at=(r=e.expires_in,Math.round(Date.now()/1e3)+r)));return{data:{session:s,user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Oi(e){const t=ji(e);return!t.error&&e.weak_password&&"object"==typeof e.weak_password&&Array.isArray(e.weak_password.reasons)&&e.weak_password.reasons.length&&e.weak_password.message&&"string"==typeof e.weak_password.message&&e.weak_password.reasons.reduce((e,t)=>e&&"string"==typeof t,!0)&&(t.data.weak_password=e.weak_password),t}function Ci(e){var t;return{data:{user:null!==(t=e.user)&&void 0!==t?t:e},error:null}}function Pi(e){return{data:e,error:null}}function Ai(e){const{action_link:t,email_otp:s,hashed_token:r,redirect_to:n,verification_type:i}=e,a=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]])}return s}(e,["action_link","email_otp","hashed_token","redirect_to","verification_type"]);return{data:{properties:{action_link:t,email_otp:s,hashed_token:r,redirect_to:n,verification_type:i},user:Object.assign({},a)},error:null}}function xi(e){return e}const Ii=["global","local","others"];class Ri{constructor({url:e="",headers:t={},fetch:s}){this.url=e,this.headers=t,this.fetch=ui(s),this.mfa={listFactors:this._listFactors.bind(this),deleteFactor:this._deleteFactor.bind(this)}}async signOut(e,t=Ii[0]){if(Ii.indexOf(t)<0)throw new Error(`@supabase/auth-js: Parameter scope must be one of ${Ii.join(", ")}`);try{return await Ti(this.fetch,"POST",`${this.url}/logout?scope=${t}`,{headers:this.headers,jwt:e,noResolveJson:!0}),{data:null,error:null}}catch(s){if(qn(s))return{data:null,error:s};throw s}}async inviteUserByEmail(e,t={}){try{return await Ti(this.fetch,"POST",`${this.url}/invite`,{body:{email:e,data:t.data},headers:this.headers,redirectTo:t.redirectTo,xform:Ci})}catch(s){if(qn(s))return{data:{user:null},error:s};throw s}}async generateLink(e){try{const{options:t}=e,s=function(e,t){var s={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&t.indexOf(r)<0&&(s[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols){var n=0;for(r=Object.getOwnPropertySymbols(e);n<r.length;n++)t.indexOf(r[n])<0&&Object.prototype.propertyIsEnumerable.call(e,r[n])&&(s[r[n]]=e[r[n]])}return s}(e,["options"]),r=Object.assign(Object.assign({},s),t);return"newEmail"in s&&(r.new_email=null==s?void 0:s.newEmail,delete r.newEmail),await Ti(this.fetch,"POST",`${this.url}/admin/generate_link`,{body:r,headers:this.headers,xform:Ai,redirectTo:null==t?void 0:t.redirectTo})}catch(t){if(qn(t))return{data:{properties:null,user:null},error:t};throw t}}async createUser(e){try{return await Ti(this.fetch,"POST",`${this.url}/admin/users`,{body:e,headers:this.headers,xform:Ci})}catch(t){if(qn(t))return{data:{user:null},error:t};throw t}}async listUsers(e){var t,s,r,n,i,a,o;try{const l={nextPage:null,lastPage:0,total:0},c=await Ti(this.fetch,"GET",`${this.url}/admin/users`,{headers:this.headers,noResolveJson:!0,query:{page:null!==(s=null===(t=null==e?void 0:e.page)||void 0===t?void 0:t.toString())&&void 0!==s?s:"",per_page:null!==(n=null===(r=null==e?void 0:e.perPage)||void 0===r?void 0:r.toString())&&void 0!==n?n:""},xform:xi});if(c.error)throw c.error;const u=await c.json(),d=null!==(i=c.headers.get("x-total-count"))&&void 0!==i?i:0,h=null!==(o=null===(a=c.headers.get("link"))||void 0===a?void 0:a.split(","))&&void 0!==o?o:[];return h.length>0&&(h.forEach(e=>{const t=parseInt(e.split(";")[0].split("=")[1].substring(0,1)),s=JSON.parse(e.split(";")[1].split("=")[1]);l[`${s}Page`]=t}),l.total=parseInt(d)),{data:Object.assign(Object.assign({},u),l),error:null}}catch(l){if(qn(l))return{data:{users:[]},error:l};throw l}}async getUserById(e){wi(e);try{return await Ti(this.fetch,"GET",`${this.url}/admin/users/${e}`,{headers:this.headers,xform:Ci})}catch(t){if(qn(t))return{data:{user:null},error:t};throw t}}async updateUserById(e,t){wi(e);try{return await Ti(this.fetch,"PUT",`${this.url}/admin/users/${e}`,{body:t,headers:this.headers,xform:Ci})}catch(s){if(qn(s))return{data:{user:null},error:s};throw s}}async deleteUser(e,t=!1){wi(e);try{return await Ti(this.fetch,"DELETE",`${this.url}/admin/users/${e}`,{headers:this.headers,body:{should_soft_delete:t},xform:Ci})}catch(s){if(qn(s))return{data:{user:null},error:s};throw s}}async _listFactors(e){wi(e.userId);try{const{data:t,error:s}=await Ti(this.fetch,"GET",`${this.url}/admin/users/${e.userId}/factors`,{headers:this.headers,xform:e=>({data:{factors:e},error:null})});return{data:t,error:s}}catch(t){if(qn(t))return{data:null,error:t};throw t}}async _deleteFactor(e){wi(e.userId),wi(e.id);try{return{data:await Ti(this.fetch,"DELETE",`${this.url}/admin/users/${e.userId}/factors/${e.id}`,{headers:this.headers}),error:null}}catch(t){if(qn(t))return{data:null,error:t};throw t}}}const $i={getItem:e=>ci()?globalThis.localStorage.getItem(e):null,setItem:(e,t)=>{ci()&&globalThis.localStorage.setItem(e,t)},removeItem:e=>{ci()&&globalThis.localStorage.removeItem(e)}};function Li(e={}){return{getItem:t=>e[t]||null,setItem:(t,s)=>{e[t]=s},removeItem:t=>{delete e[t]}}}const Ui=!!(globalThis&&ci()&&globalThis.localStorage&&"true"===globalThis.localStorage.getItem("supabase.gotrue-js.locks.debug"));class Di extends Error{constructor(e){super(e),this.isAcquireTimeout=!0}}class qi extends Di{}async function Ni(e,t,s){Ui&&console.log("@supabase/gotrue-js: navigatorLock: acquire lock",e,t);const r=new globalThis.AbortController;return t>0&&setTimeout(()=>{r.abort(),Ui&&console.log("@supabase/gotrue-js: navigatorLock acquire timed out",e)},t),await Promise.resolve().then(()=>globalThis.navigator.locks.request(e,0===t?{mode:"exclusive",ifAvailable:!0}:{mode:"exclusive",signal:r.signal},async r=>{if(!r){if(0===t)throw Ui&&console.log("@supabase/gotrue-js: navigatorLock: not immediately available",e),new qi(`Acquiring an exclusive Navigator LockManager lock "${e}" immediately failed`);if(Ui)try{const e=await globalThis.navigator.locks.query();console.log("@supabase/gotrue-js: Navigator LockManager state",JSON.stringify(e,null,"  "))}catch(n){console.warn("@supabase/gotrue-js: Error when querying Navigator LockManager state",n)}return console.warn("@supabase/gotrue-js: Navigator LockManager returned a null lock when using #request without ifAvailable set to true, it appears this browser is not following the LockManager spec https://developer.mozilla.org/en-US/docs/Web/API/LockManager/request"),await s()}Ui&&console.log("@supabase/gotrue-js: navigatorLock: acquired",e,r.name);try{return await s()}finally{Ui&&console.log("@supabase/gotrue-js: navigatorLock: released",e,r.name)}}))}!function(){if("object"!=typeof globalThis)try{Object.defineProperty(Object.prototype,"__magic__",{get:function(){return this},configurable:!0}),__magic__.globalThis=__magic__,delete Object.prototype.__magic__}catch(e){"undefined"!=typeof self&&(self.globalThis=self)}}();const Mi={url:"http://localhost:9999",storageKey:"supabase.auth.token",autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0,headers:Rn,flowType:"implicit",debug:!1,hasCustomAuthorizationHeader:!1};async function Vi(e,t,s){return await s()}class Bi{constructor(e){var t,s;this.memoryStorage=null,this.stateChangeEmitters=new Map,this.autoRefreshTicker=null,this.visibilityChangedCallback=null,this.refreshingDeferred=null,this.initializePromise=null,this.detectSessionInUrl=!0,this.hasCustomAuthorizationHeader=!1,this.suppressGetSessionWarning=!1,this.lockAcquired=!1,this.pendingInLock=[],this.broadcastChannel=null,this.logger=console.log,this.instanceID=Bi.nextInstanceID,Bi.nextInstanceID+=1,this.instanceID>0&&oi()&&console.warn("Multiple GoTrueClient instances detected in the same browser context. It is not an error, but this should be avoided as it may produce undefined behavior when used concurrently under the same storage key.");const r=Object.assign(Object.assign({},Mi),e);if(this.logDebugMessages=!!r.debug,"function"==typeof r.debug&&(this.logger=r.debug),this.persistSession=r.persistSession,this.storageKey=r.storageKey,this.autoRefreshToken=r.autoRefreshToken,this.admin=new Ri({url:r.url,headers:r.headers,fetch:r.fetch}),this.url=r.url,this.headers=r.headers,this.fetch=ui(r.fetch),this.lock=r.lock||Vi,this.detectSessionInUrl=r.detectSessionInUrl,this.flowType=r.flowType,this.hasCustomAuthorizationHeader=r.hasCustomAuthorizationHeader,r.lock?this.lock=r.lock:oi()&&(null===(t=null===globalThis||void 0===globalThis?void 0:globalThis.navigator)||void 0===t?void 0:t.locks)?this.lock=Ni:this.lock=Vi,this.jwks={keys:[]},this.jwks_cached_at=Number.MIN_SAFE_INTEGER,this.mfa={verify:this._verify.bind(this),enroll:this._enroll.bind(this),unenroll:this._unenroll.bind(this),challenge:this._challenge.bind(this),listFactors:this._listFactors.bind(this),challengeAndVerify:this._challengeAndVerify.bind(this),getAuthenticatorAssuranceLevel:this._getAuthenticatorAssuranceLevel.bind(this)},this.persistSession?r.storage?this.storage=r.storage:ci()?this.storage=$i:(this.memoryStorage={},this.storage=Li(this.memoryStorage)):(this.memoryStorage={},this.storage=Li(this.memoryStorage)),oi()&&globalThis.BroadcastChannel&&this.persistSession&&this.storageKey){try{this.broadcastChannel=new globalThis.BroadcastChannel(this.storageKey)}catch(n){console.error("Failed to create a new BroadcastChannel, multi-tab state changes will not be available",n)}null===(s=this.broadcastChannel)||void 0===s||s.addEventListener("message",async e=>{this._debug("received broadcast notification from other tab or client",e),await this._notifyAllSubscribers(e.data.event,e.data.session,!1)})}this.initialize()}_debug(...e){return this.logDebugMessages&&this.logger(`GoTrueClient@${this.instanceID} (${An}) ${(new Date).toISOString()}`,...e),this}async initialize(){return this.initializePromise||(this.initializePromise=(async()=>await this._acquireLock(-1,async()=>await this._initialize()))()),await this.initializePromise}async _initialize(){var e;try{const t=function(e){const t={},s=new URL(e);if(s.hash&&"#"===s.hash[0])try{new URLSearchParams(s.hash.substring(1)).forEach((e,s)=>{t[s]=e})}catch(r){}return s.searchParams.forEach((e,s)=>{t[s]=e}),t}(window.location.href);let s="none";if(this._isImplicitGrantCallback(t)?s="implicit":await this._isPKCECallback(t)&&(s="pkce"),oi()&&this.detectSessionInUrl&&"none"!==s){const{data:r,error:n}=await this._getSessionFromURL(t,s);if(n){if(this._debug("#_initialize()","error detecting session from URL",n),function(e){return qn(e)&&"AuthImplicitGrantRedirectError"===e.name}(n)){const t=null===(e=n.details)||void 0===e?void 0:e.code;if("identity_already_exists"===t||"identity_not_found"===t||"single_identity_not_deletable"===t)return{error:n}}return await this._removeSession(),{error:n}}const{session:i,redirectType:a}=r;return this._debug("#_initialize()","detected session in URL",i,"redirect type",a),await this._saveSession(i),setTimeout(async()=>{"recovery"===a?await this._notifyAllSubscribers("PASSWORD_RECOVERY",i):await this._notifyAllSubscribers("SIGNED_IN",i)},0),{error:null}}return await this._recoverAndRefresh(),{error:null}}catch(t){return qn(t)?{error:t}:{error:new Mn("Unexpected error during initialization",t)}}finally{await this._handleVisibilityChange(),this._debug("#_initialize()","end")}}async signInAnonymously(e){var t,s,r;try{const n=await Ti(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{data:null!==(s=null===(t=null==e?void 0:e.options)||void 0===t?void 0:t.data)&&void 0!==s?s:{},gotrue_meta_security:{captcha_token:null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken}},xform:ji}),{data:i,error:a}=n;if(a||!i)return{data:{user:null,session:null},error:a};const o=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(n){if(qn(n))return{data:{user:null,session:null},error:n};throw n}}async signUp(e){var t,s,r;try{let n;if("email"in e){const{email:s,password:r,options:i}=e;let a=null,o=null;"pkce"===this.flowType&&([a,o]=await yi(this.storage,this.storageKey)),n=await Ti(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,redirectTo:null==i?void 0:i.emailRedirectTo,body:{email:s,password:r,data:null!==(t=null==i?void 0:i.data)&&void 0!==t?t:{},gotrue_meta_security:{captcha_token:null==i?void 0:i.captchaToken},code_challenge:a,code_challenge_method:o},xform:ji})}else{if(!("phone"in e))throw new zn("You must provide either an email or phone number and a password");{const{phone:t,password:i,options:a}=e;n=await Ti(this.fetch,"POST",`${this.url}/signup`,{headers:this.headers,body:{phone:t,password:i,data:null!==(s=null==a?void 0:a.data)&&void 0!==s?s:{},channel:null!==(r=null==a?void 0:a.channel)&&void 0!==r?r:"sms",gotrue_meta_security:{captcha_token:null==a?void 0:a.captchaToken}},xform:ji})}}const{data:i,error:a}=n;if(a||!i)return{data:{user:null,session:null},error:a};const o=i.session,l=i.user;return i.session&&(await this._saveSession(i.session),await this._notifyAllSubscribers("SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(n){if(qn(n))return{data:{user:null,session:null},error:n};throw n}}async signInWithPassword(e){try{let t;if("email"in e){const{email:s,password:r,options:n}=e;t=await Ti(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{email:s,password:r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:Oi})}else{if(!("phone"in e))throw new zn("You must provide either an email or phone number and a password");{const{phone:s,password:r,options:n}=e;t=await Ti(this.fetch,"POST",`${this.url}/token?grant_type=password`,{headers:this.headers,body:{phone:s,password:r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},xform:Oi})}}const{data:s,error:r}=t;return r?{data:{user:null,session:null},error:r}:s&&s.session&&s.user?(s.session&&(await this._saveSession(s.session),await this._notifyAllSubscribers("SIGNED_IN",s.session)),{data:Object.assign({user:s.user,session:s.session},s.weak_password?{weakPassword:s.weak_password}:null),error:r}):{data:{user:null,session:null},error:new Fn}}catch(t){if(qn(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOAuth(e){var t,s,r,n;return await this._handleProviderSignIn(e.provider,{redirectTo:null===(t=e.options)||void 0===t?void 0:t.redirectTo,scopes:null===(s=e.options)||void 0===s?void 0:s.scopes,queryParams:null===(r=e.options)||void 0===r?void 0:r.queryParams,skipBrowserRedirect:null===(n=e.options)||void 0===n?void 0:n.skipBrowserRedirect})}async exchangeCodeForSession(e){return await this.initializePromise,this._acquireLock(-1,async()=>this._exchangeCodeForSession(e))}async signInWithWeb3(e){const{chain:t}=e;if("solana"===t)return await this.signInWithSolana(e);throw new Error(`@supabase/auth-js: Unsupported chain "${t}"`)}async signInWithSolana(e){var t,s,r,n,i,a,o,l,c,u,d,h;let f,p;if("message"in e)f=e.message,p=e.signature;else{const{chain:d,wallet:h,statement:m,options:v}=e;let g;if(oi())if("object"==typeof h)g=h;else{const e=window;if(!("solana"in e)||"object"!=typeof e.solana||!("signIn"in e.solana&&"function"==typeof e.solana.signIn||"signMessage"in e.solana&&"function"==typeof e.solana.signMessage))throw new Error("@supabase/auth-js: No compatible Solana wallet interface on the window object (window.solana) detected. Make sure the user already has a wallet installed and connected for this app. Prefer passing the wallet interface object directly to signInWithWeb3({ chain: 'solana', wallet: resolvedUserWallet }) instead.");g=e.solana}else{if("object"!=typeof h||!(null==v?void 0:v.url))throw new Error("@supabase/auth-js: Both wallet and url must be specified in non-browser environments.");g=h}const y=new URL(null!==(t=null==v?void 0:v.url)&&void 0!==t?t:window.location.href);if("signIn"in g&&g.signIn){const e=await g.signIn(Object.assign(Object.assign(Object.assign({issuedAt:(new Date).toISOString()},null==v?void 0:v.signInWithSolana),{version:"1",domain:y.host,uri:y.href}),m?{statement:m}:null));let t;if(Array.isArray(e)&&e[0]&&"object"==typeof e[0])t=e[0];else{if(!(e&&"object"==typeof e&&"signedMessage"in e&&"signature"in e))throw new Error("@supabase/auth-js: Wallet method signIn() returned unrecognized value");t=e}if(!("signedMessage"in t&&"signature"in t&&("string"==typeof t.signedMessage||t.signedMessage instanceof Uint8Array)&&t.signature instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet method signIn() API returned object without signedMessage and signature fields");f="string"==typeof t.signedMessage?t.signedMessage:(new TextDecoder).decode(t.signedMessage),p=t.signature}else{if(!("signMessage"in g&&"function"==typeof g.signMessage&&"publicKey"in g&&"object"==typeof g&&g.publicKey&&"toBase58"in g.publicKey&&"function"==typeof g.publicKey.toBase58))throw new Error("@supabase/auth-js: Wallet does not have a compatible signMessage() and publicKey.toBase58() API");f=[`${y.host} wants you to sign in with your Solana account:`,g.publicKey.toBase58(),...m?["",m,""]:[""],"Version: 1",`URI: ${y.href}`,`Issued At: ${null!==(r=null===(s=null==v?void 0:v.signInWithSolana)||void 0===s?void 0:s.issuedAt)&&void 0!==r?r:(new Date).toISOString()}`,...(null===(n=null==v?void 0:v.signInWithSolana)||void 0===n?void 0:n.notBefore)?[`Not Before: ${v.signInWithSolana.notBefore}`]:[],...(null===(i=null==v?void 0:v.signInWithSolana)||void 0===i?void 0:i.expirationTime)?[`Expiration Time: ${v.signInWithSolana.expirationTime}`]:[],...(null===(a=null==v?void 0:v.signInWithSolana)||void 0===a?void 0:a.chainId)?[`Chain ID: ${v.signInWithSolana.chainId}`]:[],...(null===(o=null==v?void 0:v.signInWithSolana)||void 0===o?void 0:o.nonce)?[`Nonce: ${v.signInWithSolana.nonce}`]:[],...(null===(l=null==v?void 0:v.signInWithSolana)||void 0===l?void 0:l.requestId)?[`Request ID: ${v.signInWithSolana.requestId}`]:[],...(null===(u=null===(c=null==v?void 0:v.signInWithSolana)||void 0===c?void 0:c.resources)||void 0===u?void 0:u.length)?["Resources",...v.signInWithSolana.resources.map(e=>`- ${e}`)]:[]].join("\n");const e=await g.signMessage((new TextEncoder).encode(f),"utf8");if(!(e&&e instanceof Uint8Array))throw new Error("@supabase/auth-js: Wallet signMessage() API returned an recognized value");p=e}}try{const{data:t,error:s}=await Ti(this.fetch,"POST",`${this.url}/token?grant_type=web3`,{headers:this.headers,body:Object.assign({chain:"solana",message:f,signature:ai(p)},(null===(d=e.options)||void 0===d?void 0:d.captchaToken)?{gotrue_meta_security:{captcha_token:null===(h=e.options)||void 0===h?void 0:h.captchaToken}}:null),xform:ji});if(s)throw s;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign({},t),error:s}):{data:{user:null,session:null},error:new Fn}}catch(m){if(qn(m))return{data:{user:null,session:null},error:m};throw m}}async _exchangeCodeForSession(e){const t=await hi(this.storage,`${this.storageKey}-code-verifier`),[s,r]=(null!=t?t:"").split("/");try{const{data:t,error:n}=await Ti(this.fetch,"POST",`${this.url}/token?grant_type=pkce`,{headers:this.headers,body:{auth_code:e,code_verifier:s},xform:ji});if(await fi(this.storage,`${this.storageKey}-code-verifier`),n)throw n;return t&&t.session&&t.user?(t.session&&(await this._saveSession(t.session),await this._notifyAllSubscribers("SIGNED_IN",t.session)),{data:Object.assign(Object.assign({},t),{redirectType:null!=r?r:null}),error:n}):{data:{user:null,session:null,redirectType:null},error:new Fn}}catch(n){if(qn(n))return{data:{user:null,session:null,redirectType:null},error:n};throw n}}async signInWithIdToken(e){try{const{options:t,provider:s,token:r,access_token:n,nonce:i}=e,a=await Ti(this.fetch,"POST",`${this.url}/token?grant_type=id_token`,{headers:this.headers,body:{provider:s,id_token:r,access_token:n,nonce:i,gotrue_meta_security:{captcha_token:null==t?void 0:t.captchaToken}},xform:ji}),{data:o,error:l}=a;return l?{data:{user:null,session:null},error:l}:o&&o.session&&o.user?(o.session&&(await this._saveSession(o.session),await this._notifyAllSubscribers("SIGNED_IN",o.session)),{data:o,error:l}):{data:{user:null,session:null},error:new Fn}}catch(t){if(qn(t))return{data:{user:null,session:null},error:t};throw t}}async signInWithOtp(e){var t,s,r,n,i;try{if("email"in e){const{email:r,options:n}=e;let i=null,a=null;"pkce"===this.flowType&&([i,a]=await yi(this.storage,this.storageKey));const{error:o}=await Ti(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{email:r,data:null!==(t=null==n?void 0:n.data)&&void 0!==t?t:{},create_user:null===(s=null==n?void 0:n.shouldCreateUser)||void 0===s||s,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken},code_challenge:i,code_challenge_method:a},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:o}}if("phone"in e){const{phone:t,options:s}=e,{data:a,error:o}=await Ti(this.fetch,"POST",`${this.url}/otp`,{headers:this.headers,body:{phone:t,data:null!==(r=null==s?void 0:s.data)&&void 0!==r?r:{},create_user:null===(n=null==s?void 0:s.shouldCreateUser)||void 0===n||n,gotrue_meta_security:{captcha_token:null==s?void 0:s.captchaToken},channel:null!==(i=null==s?void 0:s.channel)&&void 0!==i?i:"sms"}});return{data:{user:null,session:null,messageId:null==a?void 0:a.message_id},error:o}}throw new zn("You must provide either an email or phone number.")}catch(a){if(qn(a))return{data:{user:null,session:null},error:a};throw a}}async verifyOtp(e){var t,s;try{let r,n;"options"in e&&(r=null===(t=e.options)||void 0===t?void 0:t.redirectTo,n=null===(s=e.options)||void 0===s?void 0:s.captchaToken);const{data:i,error:a}=await Ti(this.fetch,"POST",`${this.url}/verify`,{headers:this.headers,body:Object.assign(Object.assign({},e),{gotrue_meta_security:{captcha_token:n}}),redirectTo:r,xform:ji});if(a)throw a;if(!i)throw new Error("An error occurred on token verification.");const o=i.session,l=i.user;return(null==o?void 0:o.access_token)&&(await this._saveSession(o),await this._notifyAllSubscribers("recovery"==e.type?"PASSWORD_RECOVERY":"SIGNED_IN",o)),{data:{user:l,session:o},error:null}}catch(r){if(qn(r))return{data:{user:null,session:null},error:r};throw r}}async signInWithSSO(e){var t,s,r;try{let n=null,i=null;return"pkce"===this.flowType&&([n,i]=await yi(this.storage,this.storageKey)),await Ti(this.fetch,"POST",`${this.url}/sso`,{body:Object.assign(Object.assign(Object.assign(Object.assign(Object.assign({},"providerId"in e?{provider_id:e.providerId}:null),"domain"in e?{domain:e.domain}:null),{redirect_to:null!==(s=null===(t=e.options)||void 0===t?void 0:t.redirectTo)&&void 0!==s?s:void 0}),(null===(r=null==e?void 0:e.options)||void 0===r?void 0:r.captchaToken)?{gotrue_meta_security:{captcha_token:e.options.captchaToken}}:null),{skip_http_redirect:!0,code_challenge:n,code_challenge_method:i}),headers:this.headers,xform:Pi})}catch(n){if(qn(n))return{data:null,error:n};throw n}}async reauthenticate(){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._reauthenticate())}async _reauthenticate(){try{return await this._useSession(async e=>{const{data:{session:t},error:s}=e;if(s)throw s;if(!t)throw new Bn;const{error:r}=await Ti(this.fetch,"GET",`${this.url}/reauthenticate`,{headers:this.headers,jwt:t.access_token});return{data:{user:null,session:null},error:r}})}catch(e){if(qn(e))return{data:{user:null,session:null},error:e};throw e}}async resend(e){try{const t=`${this.url}/resend`;if("email"in e){const{email:s,type:r,options:n}=e,{error:i}=await Ti(this.fetch,"POST",t,{headers:this.headers,body:{email:s,type:r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}},redirectTo:null==n?void 0:n.emailRedirectTo});return{data:{user:null,session:null},error:i}}if("phone"in e){const{phone:s,type:r,options:n}=e,{data:i,error:a}=await Ti(this.fetch,"POST",t,{headers:this.headers,body:{phone:s,type:r,gotrue_meta_security:{captcha_token:null==n?void 0:n.captchaToken}}});return{data:{user:null,session:null,messageId:null==i?void 0:i.message_id},error:a}}throw new zn("You must provide either an email or phone number and a type")}catch(t){if(qn(t))return{data:{user:null,session:null},error:t};throw t}}async getSession(){await this.initializePromise;return await this._acquireLock(-1,async()=>this._useSession(async e=>e))}async _acquireLock(e,t){this._debug("#_acquireLock","begin",e);try{if(this.lockAcquired){const e=this.pendingInLock.length?this.pendingInLock[this.pendingInLock.length-1]:Promise.resolve(),s=(async()=>(await e,await t()))();return this.pendingInLock.push((async()=>{try{await s}catch(e){}})()),s}return await this.lock(`lock:${this.storageKey}`,e,async()=>{this._debug("#_acquireLock","lock acquired for storage key",this.storageKey);try{this.lockAcquired=!0;const e=t();for(this.pendingInLock.push((async()=>{try{await e}catch(t){}})()),await e;this.pendingInLock.length;){const e=[...this.pendingInLock];await Promise.all(e),this.pendingInLock.splice(0,e.length)}return await e}finally{this._debug("#_acquireLock","lock released for storage key",this.storageKey),this.lockAcquired=!1}})}finally{this._debug("#_acquireLock","end")}}async _useSession(e){this._debug("#_useSession","begin");try{const t=await this.__loadSession();return await e(t)}finally{this._debug("#_useSession","end")}}async __loadSession(){this._debug("#__loadSession()","begin"),this.lockAcquired||this._debug("#__loadSession()","used outside of an acquired lock!",(new Error).stack);try{let e=null;const t=await hi(this.storage,this.storageKey);if(this._debug("#getSession()","session from storage",t),null!==t&&(this._isValidSession(t)?e=t:(this._debug("#getSession()","session from storage is not valid"),await this._removeSession())),!e)return{data:{session:null},error:null};const s=!!e.expires_at&&1e3*e.expires_at-Date.now()<In;if(this._debug("#__loadSession()",`session has${s?"":" not"} expired`,"expires_at",e.expires_at),!s){if(this.storage.isServer){let t=this.suppressGetSessionWarning;e=new Proxy(e,{get:(e,s,r)=>(t||"user"!==s||(console.warn("Using the user object as returned from supabase.auth.getSession() or from some supabase.auth.onAuthStateChange() events could be insecure! This value comes directly from the storage medium (usually cookies on the server) and may not be authentic. Use supabase.auth.getUser() instead which authenticates the data by contacting the Supabase Auth server."),t=!0,this.suppressGetSessionWarning=!0),Reflect.get(e,s,r))})}return{data:{session:e},error:null}}const{session:r,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{session:null},error:n}:{data:{session:r},error:null}}finally{this._debug("#__loadSession()","end")}}async getUser(e){if(e)return await this._getUser(e);await this.initializePromise;return await this._acquireLock(-1,async()=>await this._getUser())}async _getUser(e){try{return e?await Ti(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:e,xform:Ci}):await this._useSession(async e=>{var t,s,r;const{data:n,error:i}=e;if(i)throw i;return(null===(t=n.session)||void 0===t?void 0:t.access_token)||this.hasCustomAuthorizationHeader?await Ti(this.fetch,"GET",`${this.url}/user`,{headers:this.headers,jwt:null!==(r=null===(s=n.session)||void 0===s?void 0:s.access_token)&&void 0!==r?r:void 0,xform:Ci}):{data:{user:null},error:new Bn}})}catch(t){if(qn(t))return function(e){return qn(e)&&"AuthSessionMissingError"===e.name}(t)&&(await this._removeSession(),await fi(this.storage,`${this.storageKey}-code-verifier`)),{data:{user:null},error:t};throw t}}async updateUser(e,t={}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._updateUser(e,t))}async _updateUser(e,t={}){try{return await this._useSession(async s=>{const{data:r,error:n}=s;if(n)throw n;if(!r.session)throw new Bn;const i=r.session;let a=null,o=null;"pkce"===this.flowType&&null!=e.email&&([a,o]=await yi(this.storage,this.storageKey));const{data:l,error:c}=await Ti(this.fetch,"PUT",`${this.url}/user`,{headers:this.headers,redirectTo:null==t?void 0:t.emailRedirectTo,body:Object.assign(Object.assign({},e),{code_challenge:a,code_challenge_method:o}),jwt:i.access_token,xform:Ci});if(c)throw c;return i.user=l.user,await this._saveSession(i),await this._notifyAllSubscribers("USER_UPDATED",i),{data:{user:i.user},error:null}})}catch(s){if(qn(s))return{data:{user:null},error:s};throw s}}async setSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._setSession(e))}async _setSession(e){try{if(!e.access_token||!e.refresh_token)throw new Bn;const t=Date.now()/1e3;let s=t,r=!0,n=null;const{payload:i}=mi(e.access_token);if(i.exp&&(s=i.exp,r=s<=t),r){const{session:t,error:s}=await this._callRefreshToken(e.refresh_token);if(s)return{data:{user:null,session:null},error:s};if(!t)return{data:{user:null,session:null},error:null};n=t}else{const{data:r,error:i}=await this._getUser(e.access_token);if(i)throw i;n={access_token:e.access_token,refresh_token:e.refresh_token,user:r.user,token_type:"bearer",expires_in:s-t,expires_at:s},await this._saveSession(n),await this._notifyAllSubscribers("SIGNED_IN",n)}return{data:{user:n.user,session:n},error:null}}catch(t){if(qn(t))return{data:{session:null,user:null},error:t};throw t}}async refreshSession(e){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._refreshSession(e))}async _refreshSession(e){try{return await this._useSession(async t=>{var s;if(!e){const{data:r,error:n}=t;if(n)throw n;e=null!==(s=r.session)&&void 0!==s?s:void 0}if(!(null==e?void 0:e.refresh_token))throw new Bn;const{session:r,error:n}=await this._callRefreshToken(e.refresh_token);return n?{data:{user:null,session:null},error:n}:r?{data:{user:r.user,session:r},error:null}:{data:{user:null,session:null},error:null}})}catch(t){if(qn(t))return{data:{user:null,session:null},error:t};throw t}}async _getSessionFromURL(e,t){try{if(!oi())throw new Wn("No browser detected.");if(e.error||e.error_description||e.error_code)throw new Wn(e.error_description||"Error in URL with unspecified error_description",{error:e.error||"unspecified_error",code:e.error_code||"unspecified_code"});switch(t){case"implicit":if("pkce"===this.flowType)throw new Jn("Not a valid PKCE flow url.");break;case"pkce":if("implicit"===this.flowType)throw new Wn("Not a valid implicit grant flow url.")}if("pkce"===t){if(this._debug("#_initialize()","begin","is PKCE flow",!0),!e.code)throw new Jn("No code detected.");const{data:t,error:s}=await this._exchangeCodeForSession(e.code);if(s)throw s;const r=new URL(window.location.href);return r.searchParams.delete("code"),window.history.replaceState(window.history.state,"",r.toString()),{data:{session:t.session,redirectType:null},error:null}}const{provider_token:s,provider_refresh_token:r,access_token:n,refresh_token:i,expires_in:a,expires_at:o,token_type:l}=e;if(!(n&&a&&i&&l))throw new Wn("No session defined in URL");const c=Math.round(Date.now()/1e3),u=parseInt(a);let d=c+u;o&&(d=parseInt(o));const h=d-c;1e3*h<=xn&&console.warn(`@supabase/gotrue-js: Session as retrieved from URL expires in ${h}s, should have been closer to ${u}s`);const f=d-u;c-f>=120?console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued over 120s ago, URL could be stale",f,d,c):c-f<0&&console.warn("@supabase/gotrue-js: Session as retrieved from URL was issued in the future? Check the device clock for skew",f,d,c);const{data:p,error:m}=await this._getUser(n);if(m)throw m;const v={provider_token:s,provider_refresh_token:r,access_token:n,expires_in:u,expires_at:d,refresh_token:i,token_type:l,user:p.user};return window.location.hash="",this._debug("#_getSessionFromURL()","clearing window.location.hash"),{data:{session:v,redirectType:e.type},error:null}}catch(s){if(qn(s))return{data:{session:null,redirectType:null},error:s};throw s}}_isImplicitGrantCallback(e){return Boolean(e.access_token||e.error_description)}async _isPKCECallback(e){const t=await hi(this.storage,`${this.storageKey}-code-verifier`);return!(!e.code||!t)}async signOut(e={scope:"global"}){return await this.initializePromise,await this._acquireLock(-1,async()=>await this._signOut(e))}async _signOut({scope:e}={scope:"global"}){return await this._useSession(async t=>{var s;const{data:r,error:n}=t;if(n)return{error:n};const i=null===(s=r.session)||void 0===s?void 0:s.access_token;if(i){const{error:t}=await this.admin.signOut(i,e);if(t&&(!function(e){return qn(e)&&"AuthApiError"===e.name}(t)||404!==t.status&&401!==t.status&&403!==t.status))return{error:t}}return"others"!==e&&(await this._removeSession(),await fi(this.storage,`${this.storageKey}-code-verifier`)),{error:null}})}onAuthStateChange(e){const t="xxxxxxxx-xxxx-4xxx-yxxx-xxxxxxxxxxxx".replace(/[xy]/g,function(e){const t=16*Math.random()|0;return("x"==e?t:3&t|8).toString(16)}),s={id:t,callback:e,unsubscribe:()=>{this._debug("#unsubscribe()","state change callback with id removed",t),this.stateChangeEmitters.delete(t)}};return this._debug("#onAuthStateChange()","registered callback with id",t),this.stateChangeEmitters.set(t,s),(async()=>{await this.initializePromise,await this._acquireLock(-1,async()=>{this._emitInitialSession(t)})})(),{data:{subscription:s}}}async _emitInitialSession(e){return await this._useSession(async t=>{var s,r;try{const{data:{session:r},error:n}=t;if(n)throw n;await(null===(s=this.stateChangeEmitters.get(e))||void 0===s?void 0:s.callback("INITIAL_SESSION",r)),this._debug("INITIAL_SESSION","callback id",e,"session",r)}catch(n){await(null===(r=this.stateChangeEmitters.get(e))||void 0===r?void 0:r.callback("INITIAL_SESSION",null)),this._debug("INITIAL_SESSION","callback id",e,"error",n),console.error(n)}})}async resetPasswordForEmail(e,t={}){let s=null,r=null;"pkce"===this.flowType&&([s,r]=await yi(this.storage,this.storageKey,!0));try{return await Ti(this.fetch,"POST",`${this.url}/recover`,{body:{email:e,code_challenge:s,code_challenge_method:r,gotrue_meta_security:{captcha_token:t.captchaToken}},headers:this.headers,redirectTo:t.redirectTo})}catch(n){if(qn(n))return{data:null,error:n};throw n}}async getUserIdentities(){var e;try{const{data:t,error:s}=await this.getUser();if(s)throw s;return{data:{identities:null!==(e=t.user.identities)&&void 0!==e?e:[]},error:null}}catch(t){if(qn(t))return{data:null,error:t};throw t}}async linkIdentity(e){var t;try{const{data:s,error:r}=await this._useSession(async t=>{var s,r,n,i,a;const{data:o,error:l}=t;if(l)throw l;const c=await this._getUrlForProvider(`${this.url}/user/identities/authorize`,e.provider,{redirectTo:null===(s=e.options)||void 0===s?void 0:s.redirectTo,scopes:null===(r=e.options)||void 0===r?void 0:r.scopes,queryParams:null===(n=e.options)||void 0===n?void 0:n.queryParams,skipBrowserRedirect:!0});return await Ti(this.fetch,"GET",c,{headers:this.headers,jwt:null!==(a=null===(i=o.session)||void 0===i?void 0:i.access_token)&&void 0!==a?a:void 0})});if(r)throw r;return oi()&&!(null===(t=e.options)||void 0===t?void 0:t.skipBrowserRedirect)&&window.location.assign(null==s?void 0:s.url),{data:{provider:e.provider,url:null==s?void 0:s.url},error:null}}catch(s){if(qn(s))return{data:{provider:e.provider,url:null},error:s};throw s}}async unlinkIdentity(e){try{return await this._useSession(async t=>{var s,r;const{data:n,error:i}=t;if(i)throw i;return await Ti(this.fetch,"DELETE",`${this.url}/user/identities/${e.identity_id}`,{headers:this.headers,jwt:null!==(r=null===(s=n.session)||void 0===s?void 0:s.access_token)&&void 0!==r?r:void 0})})}catch(t){if(qn(t))return{data:null,error:t};throw t}}async _refreshAccessToken(e){const t=`#_refreshAccessToken(${e.substring(0,5)}...)`;this._debug(t,"begin");try{const n=Date.now();return await(s=async s=>(s>0&&await async function(e){return await new Promise(t=>{setTimeout(()=>t(null),e)})}(200*Math.pow(2,s-1)),this._debug(t,"refreshing attempt",s),await Ti(this.fetch,"POST",`${this.url}/token?grant_type=refresh_token`,{body:{refresh_token:e},headers:this.headers,xform:ji})),r=(e,t)=>{const s=200*Math.pow(2,e);return t&&Kn(t)&&Date.now()+s-n<xn},new Promise((e,t)=>{(async()=>{for(let i=0;i<1/0;i++)try{const t=await s(i);if(!r(i,null,t))return void e(t)}catch(n){if(!r(i,n))return void t(n)}})()}))}catch(n){if(this._debug(t,"error",n),qn(n))return{data:{session:null,user:null},error:n};throw n}finally{this._debug(t,"end")}var s,r}_isValidSession(e){return"object"==typeof e&&null!==e&&"access_token"in e&&"refresh_token"in e&&"expires_at"in e}async _handleProviderSignIn(e,t){const s=await this._getUrlForProvider(`${this.url}/authorize`,e,{redirectTo:t.redirectTo,scopes:t.scopes,queryParams:t.queryParams});return this._debug("#_handleProviderSignIn()","provider",e,"options",t,"url",s),oi()&&!t.skipBrowserRedirect&&window.location.assign(s),{data:{provider:e,url:s},error:null}}async _recoverAndRefresh(){var e;const t="#_recoverAndRefresh()";this._debug(t,"begin");try{const s=await hi(this.storage,this.storageKey);if(this._debug(t,"session from storage",s),!this._isValidSession(s))return this._debug(t,"session is not valid"),void(null!==s&&await this._removeSession());const r=1e3*(null!==(e=s.expires_at)&&void 0!==e?e:1/0)-Date.now()<In;if(this._debug(t,`session has${r?"":" not"} expired with margin of 90000s`),r){if(this.autoRefreshToken&&s.refresh_token){const{error:e}=await this._callRefreshToken(s.refresh_token);e&&(console.error(e),Kn(e)||(this._debug(t,"refresh failed with a non-retryable error, removing the session",e),await this._removeSession()))}}else await this._notifyAllSubscribers("SIGNED_IN",s)}catch(s){return this._debug(t,"error",s),void console.error(s)}finally{this._debug(t,"end")}}async _callRefreshToken(e){var t,s;if(!e)throw new Bn;if(this.refreshingDeferred)return this.refreshingDeferred.promise;const r=`#_callRefreshToken(${e.substring(0,5)}...)`;this._debug(r,"begin");try{this.refreshingDeferred=new pi;const{data:t,error:s}=await this._refreshAccessToken(e);if(s)throw s;if(!t.session)throw new Bn;await this._saveSession(t.session),await this._notifyAllSubscribers("TOKEN_REFRESHED",t.session);const r={session:t.session,error:null};return this.refreshingDeferred.resolve(r),r}catch(n){if(this._debug(r,"error",n),qn(n)){const e={session:null,error:n};return Kn(n)||await this._removeSession(),null===(t=this.refreshingDeferred)||void 0===t||t.resolve(e),e}throw null===(s=this.refreshingDeferred)||void 0===s||s.reject(n),n}finally{this.refreshingDeferred=null,this._debug(r,"end")}}async _notifyAllSubscribers(e,t,s=!0){const r=`#_notifyAllSubscribers(${e})`;this._debug(r,"begin",t,`broadcast = ${s}`);try{this.broadcastChannel&&s&&this.broadcastChannel.postMessage({event:e,session:t});const r=[],n=Array.from(this.stateChangeEmitters.values()).map(async s=>{try{await s.callback(e,t)}catch(n){r.push(n)}});if(await Promise.all(n),r.length>0){for(let e=0;e<r.length;e+=1)console.error(r[e]);throw r[0]}}finally{this._debug(r,"end")}}async _saveSession(e){this._debug("#_saveSession()",e),this.suppressGetSessionWarning=!0,await di(this.storage,this.storageKey,e)}async _removeSession(){this._debug("#_removeSession()"),await fi(this.storage,this.storageKey),await this._notifyAllSubscribers("SIGNED_OUT",null)}_removeVisibilityChangedCallback(){this._debug("#_removeVisibilityChangedCallback()");const e=this.visibilityChangedCallback;this.visibilityChangedCallback=null;try{e&&oi()&&(null===window||void 0===window?void 0:window.removeEventListener)&&window.removeEventListener("visibilitychange",e)}catch(t){console.error("removing visibilitychange callback failed",t)}}async _startAutoRefresh(){await this._stopAutoRefresh(),this._debug("#_startAutoRefresh()");const e=setInterval(()=>this._autoRefreshTokenTick(),xn);this.autoRefreshTicker=e,e&&"object"==typeof e&&"function"==typeof e.unref?e.unref():"undefined"!=typeof Deno&&"function"==typeof Deno.unrefTimer&&Deno.unrefTimer(e),setTimeout(async()=>{await this.initializePromise,await this._autoRefreshTokenTick()},0)}async _stopAutoRefresh(){this._debug("#_stopAutoRefresh()");const e=this.autoRefreshTicker;this.autoRefreshTicker=null,e&&clearInterval(e)}async startAutoRefresh(){this._removeVisibilityChangedCallback(),await this._startAutoRefresh()}async stopAutoRefresh(){this._removeVisibilityChangedCallback(),await this._stopAutoRefresh()}async _autoRefreshTokenTick(){this._debug("#_autoRefreshTokenTick()","begin");try{await this._acquireLock(0,async()=>{try{const t=Date.now();try{return await this._useSession(async e=>{const{data:{session:s}}=e;if(!s||!s.refresh_token||!s.expires_at)return void this._debug("#_autoRefreshTokenTick()","no session");const r=Math.floor((1e3*s.expires_at-t)/xn);this._debug("#_autoRefreshTokenTick()",`access token expires in ${r} ticks, a tick lasts 30000ms, refresh threshold is 3 ticks`),r<=3&&await this._callRefreshToken(s.refresh_token)})}catch(e){console.error("Auto refresh tick failed with error. This is likely a transient error.",e)}}finally{this._debug("#_autoRefreshTokenTick()","end")}})}catch(e){if(!(e.isAcquireTimeout||e instanceof Di))throw e;this._debug("auto refresh token tick lock not available")}}async _handleVisibilityChange(){if(this._debug("#_handleVisibilityChange()"),!oi()||!(null===window||void 0===window?void 0:window.addEventListener))return this.autoRefreshToken&&this.startAutoRefresh(),!1;try{this.visibilityChangedCallback=async()=>await this._onVisibilityChanged(!1),null===window||void 0===window||window.addEventListener("visibilitychange",this.visibilityChangedCallback),await this._onVisibilityChanged(!0)}catch(e){console.error("_handleVisibilityChange",e)}}async _onVisibilityChanged(e){const t=`#_onVisibilityChanged(${e})`;this._debug(t,"visibilityState",document.visibilityState),"visible"===document.visibilityState?(this.autoRefreshToken&&this._startAutoRefresh(),e||(await this.initializePromise,await this._acquireLock(-1,async()=>{"visible"===document.visibilityState?await this._recoverAndRefresh():this._debug(t,"acquired the lock to recover the session, but the browser visibilityState is no longer visible, aborting")}))):"hidden"===document.visibilityState&&this.autoRefreshToken&&this._stopAutoRefresh()}async _getUrlForProvider(e,t,s){const r=[`provider=${encodeURIComponent(t)}`];if((null==s?void 0:s.redirectTo)&&r.push(`redirect_to=${encodeURIComponent(s.redirectTo)}`),(null==s?void 0:s.scopes)&&r.push(`scopes=${encodeURIComponent(s.scopes)}`),"pkce"===this.flowType){const[e,t]=await yi(this.storage,this.storageKey),s=new URLSearchParams({code_challenge:`${encodeURIComponent(e)}`,code_challenge_method:`${encodeURIComponent(t)}`});r.push(s.toString())}if(null==s?void 0:s.queryParams){const e=new URLSearchParams(s.queryParams);r.push(e.toString())}return(null==s?void 0:s.skipBrowserRedirect)&&r.push(`skip_http_redirect=${s.skipBrowserRedirect}`),`${e}?${r.join("&")}`}async _unenroll(e){try{return await this._useSession(async t=>{var s;const{data:r,error:n}=t;return n?{data:null,error:n}:await Ti(this.fetch,"DELETE",`${this.url}/factors/${e.factorId}`,{headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token})})}catch(t){if(qn(t))return{data:null,error:t};throw t}}async _enroll(e){try{return await this._useSession(async t=>{var s,r;const{data:n,error:i}=t;if(i)return{data:null,error:i};const a=Object.assign({friendly_name:e.friendlyName,factor_type:e.factorType},"phone"===e.factorType?{phone:e.phone}:{issuer:e.issuer}),{data:o,error:l}=await Ti(this.fetch,"POST",`${this.url}/factors`,{body:a,headers:this.headers,jwt:null===(s=null==n?void 0:n.session)||void 0===s?void 0:s.access_token});return l?{data:null,error:l}:("totp"===e.factorType&&(null===(r=null==o?void 0:o.totp)||void 0===r?void 0:r.qr_code)&&(o.totp.qr_code=`data:image/svg+xml;utf-8,${o.totp.qr_code}`),{data:o,error:null})})}catch(t){if(qn(t))return{data:null,error:t};throw t}}async _verify(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;const{data:r,error:n}=t;if(n)return{data:null,error:n};const{data:i,error:a}=await Ti(this.fetch,"POST",`${this.url}/factors/${e.factorId}/verify`,{body:{code:e.code,challenge_id:e.challengeId},headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token});return a?{data:null,error:a}:(await this._saveSession(Object.assign({expires_at:Math.round(Date.now()/1e3)+i.expires_in},i)),await this._notifyAllSubscribers("MFA_CHALLENGE_VERIFIED",i),{data:i,error:a})})}catch(t){if(qn(t))return{data:null,error:t};throw t}})}async _challenge(e){return this._acquireLock(-1,async()=>{try{return await this._useSession(async t=>{var s;const{data:r,error:n}=t;return n?{data:null,error:n}:await Ti(this.fetch,"POST",`${this.url}/factors/${e.factorId}/challenge`,{body:{channel:e.channel},headers:this.headers,jwt:null===(s=null==r?void 0:r.session)||void 0===s?void 0:s.access_token})})}catch(t){if(qn(t))return{data:null,error:t};throw t}})}async _challengeAndVerify(e){const{data:t,error:s}=await this._challenge({factorId:e.factorId});return s?{data:null,error:s}:await this._verify({factorId:e.factorId,challengeId:t.id,code:e.code})}async _listFactors(){const{data:{user:e},error:t}=await this.getUser();if(t)return{data:null,error:t};const s=(null==e?void 0:e.factors)||[],r=s.filter(e=>"totp"===e.factor_type&&"verified"===e.status),n=s.filter(e=>"phone"===e.factor_type&&"verified"===e.status);return{data:{all:s,totp:r,phone:n},error:null}}async _getAuthenticatorAssuranceLevel(){return this._acquireLock(-1,async()=>await this._useSession(async e=>{var t,s;const{data:{session:r},error:n}=e;if(n)return{data:null,error:n};if(!r)return{data:{currentLevel:null,nextLevel:null,currentAuthenticationMethods:[]},error:null};const{payload:i}=mi(r.access_token);let a=null;i.aal&&(a=i.aal);let o=a;(null!==(s=null===(t=r.user.factors)||void 0===t?void 0:t.filter(e=>"verified"===e.status))&&void 0!==s?s:[]).length>0&&(o="aal2");return{data:{currentLevel:a,nextLevel:o,currentAuthenticationMethods:i.amr||[]},error:null}}))}async fetchJwk(e,t={keys:[]}){let s=t.keys.find(t=>t.kid===e);if(s)return s;if(s=this.jwks.keys.find(t=>t.kid===e),s&&this.jwks_cached_at+6e5>Date.now())return s;const{data:r,error:n}=await Ti(this.fetch,"GET",`${this.url}/.well-known/jwks.json`,{headers:this.headers});if(n)throw n;if(!r.keys||0===r.keys.length)throw new Qn("JWKS is empty");if(this.jwks=r,this.jwks_cached_at=Date.now(),s=r.keys.find(t=>t.kid===e),!s)throw new Qn("No matching signing key found in JWKS");return s}async getClaims(e,t={keys:[]}){try{let s=e;if(!s){const{data:e,error:t}=await this.getSession();if(t||!e.session)return{data:null,error:t};s=e.session.access_token}const{header:r,payload:n,signature:i,raw:{header:a,payload:o}}=mi(s);if(function(e){if(!e)throw new Error("Missing exp claim");if(e<=Math.floor(Date.now()/1e3))throw new Error("JWT has expired")}(n.exp),!r.kid||"HS256"===r.alg||!("crypto"in globalThis)||!("subtle"in globalThis.crypto)){const{error:e}=await this.getUser(s);if(e)throw e;return{data:{claims:n,header:r,signature:i},error:null}}const l=function(e){switch(e){case"RS256":return{name:"RSASSA-PKCS1-v1_5",hash:{name:"SHA-256"}};case"ES256":return{name:"ECDSA",namedCurve:"P-256",hash:{name:"SHA-256"}};default:throw new Error("Invalid alg claim")}}(r.alg),c=await this.fetchJwk(r.kid,t),u=await crypto.subtle.importKey("jwk",c,l,!0,["verify"]);if(!(await crypto.subtle.verify(l,u,i,ii(`${a}.${o}`))))throw new Qn("Invalid JWT signature");return{data:{claims:n,header:r,signature:i},error:null}}catch(s){if(qn(s))return{data:null,error:s};throw s}}}Bi.nextInstanceID=0;const Fi=Bi;class zi extends Fi{constructor(e){super(e)}}var Wi=function(e,t,s,r){return new(s||(s=Promise))(function(n,i){function a(e){try{l(r.next(e))}catch(t){i(t)}}function o(e){try{l(r.throw(e))}catch(t){i(t)}}function l(e){var t;e.done?n(e.value):(t=e.value,t instanceof s?t:new s(function(e){e(t)})).then(a,o)}l((r=r.apply(e,t||[])).next())})};class Ji{constructor(e,t,s){var r,n,i;if(this.supabaseUrl=e,this.supabaseKey=t,!e)throw new Error("supabaseUrl is required.");if(!t)throw new Error("supabaseKey is required.");const a=(o=e).endsWith("/")?o:o+"/";var o;const l=new URL(a);this.realtimeUrl=new URL("realtime/v1",l),this.realtimeUrl.protocol=this.realtimeUrl.protocol.replace("http","ws"),this.authUrl=new URL("auth/v1",l),this.storageUrl=new URL("storage/v1",l),this.functionsUrl=new URL("functions/v1",l);const c=`sb-${l.hostname.split(".")[0]}-auth-token`,u=function(e,t){var s,r;const{db:n,auth:i,realtime:a,global:o}=e,{db:l,auth:c,realtime:u,global:d}=t,h={db:Object.assign(Object.assign({},l),n),auth:Object.assign(Object.assign({},c),i),realtime:Object.assign(Object.assign({},u),a),global:Object.assign(Object.assign(Object.assign({},d),o),{headers:Object.assign(Object.assign({},null!==(s=null==d?void 0:d.headers)&&void 0!==s?s:{}),null!==(r=null==o?void 0:o.headers)&&void 0!==r?r:{})}),accessToken:()=>Pn(this,void 0,void 0,function*(){return""})};return e.accessToken?h.accessToken=e.accessToken:delete h.accessToken,h}(null!=s?s:{},{db:En,realtime:jn,auth:Object.assign(Object.assign({},Tn),{storageKey:c}),global:Sn});this.storageKey=null!==(r=u.auth.storageKey)&&void 0!==r?r:"",this.headers=null!==(n=u.global.headers)&&void 0!==n?n:{},u.accessToken?(this.accessToken=u.accessToken,this.auth=new Proxy({},{get:(e,t)=>{throw new Error(`@supabase/supabase-js: Supabase Client is configured with the accessToken option, accessing supabase.auth.${String(t)} is not possible`)}})):this.auth=this._initSupabaseAuthClient(null!==(i=u.auth)&&void 0!==i?i:{},this.headers,u.global.fetch),this.fetch=Cn(t,this._getAccessToken.bind(this),u.global.fetch),this.realtime=this._initRealtimeClient(Object.assign({headers:this.headers,accessToken:this._getAccessToken.bind(this)},u.realtime)),this.rest=new ur(new URL("rest/v1",l).href,{headers:this.headers,schema:u.db.schema,fetch:this.fetch}),u.accessToken||this._listenForAuthEvents()}get functions(){return new bs(this.functionsUrl.href,{headers:this.headers,customFetch:this.fetch})}get storage(){return new wn(this.storageUrl.href,this.headers,this.fetch)}from(e){return this.rest.from(e)}schema(e){return this.rest.schema(e)}rpc(e,t={},s={}){return this.rest.rpc(e,t,s)}channel(e,t={config:{}}){return this.realtime.channel(e,t)}getChannels(){return this.realtime.getChannels()}removeChannel(e){return this.realtime.removeChannel(e)}removeAllChannels(){return this.realtime.removeAllChannels()}_getAccessToken(){var e,t;return Wi(this,void 0,void 0,function*(){if(this.accessToken)return yield this.accessToken();const{data:s}=yield this.auth.getSession();return null!==(t=null===(e=s.session)||void 0===e?void 0:e.access_token)&&void 0!==t?t:null})}_initSupabaseAuthClient({autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,storageKey:n,flowType:i,lock:a,debug:o},l,c){const u={Authorization:`Bearer ${this.supabaseKey}`,apikey:`${this.supabaseKey}`};return new zi({url:this.authUrl.href,headers:Object.assign(Object.assign({},u),l),storageKey:n,autoRefreshToken:e,persistSession:t,detectSessionInUrl:s,storage:r,flowType:i,lock:a,debug:o,fetch:c,hasCustomAuthorizationHeader:"Authorization"in this.headers})}_initRealtimeClient(e){return new Zr(this.realtimeUrl.href,Object.assign(Object.assign({},e),{params:Object.assign({apikey:this.supabaseKey},null==e?void 0:e.params)}))}_listenForAuthEvents(){return this.auth.onAuthStateChange((e,t)=>{this._handleTokenChanged(e,"CLIENT",null==t?void 0:t.access_token)})}_handleTokenChanged(e,t,s){"TOKEN_REFRESHED"!==e&&"SIGNED_IN"!==e||this.changedAccessToken===s?"SIGNED_OUT"===e&&(this.realtime.setAuth(),"STORAGE"==t&&this.auth.signOut(),this.changedAccessToken=void 0):this.changedAccessToken=s}}const Gi=new Ji("https://fytiwsutzgmygfxnqoft.supabase.co","eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImZ5dGl3c3V0emdteWdmeG5xb2Z0Iiwicm9sZSI6ImFub24iLCJpYXQiOjE3NTA4MDM1ODcsImV4cCI6MjA2NjM3OTU4N30.LM9vazR9QCZ4vLC_Q1lJmtCj3pEVqM6vpW4TKzntAQA",{auth:{autoRefreshToken:!0,persistSession:!0,detectSessionInUrl:!0}});const Ki={TOOLS:"tools",CATEGORIES:"categories",PRODUCTS:"products",PRODUCT_CATEGORIES:"product_categories",USERS:"users",USER_PROFILES:"user_profiles",FAVORITES:"favorites",ORDERS:"orders",ORDER_ITEMS:"order_items",PAYMENTS:"payments",REVIEWS:"reviews",TAGS:"tags",TOOL_TAGS:"tool_tags",ANALYTICS:"analytics"},Hi=e=>(console.error("Supabase Error:",e),(null==e?void 0:e.message)?e.message:(null==e?void 0:e.error_description)?e.error_description:"操作失败，请稍后重试"),Qi=p("tools",()=>{const e=l([]),t=l(!1),s=l(null),r=l(!1),n=l(""),i=l("all"),o=l(!1),c=a(()=>{if(!n.value)return e.value;const t=n.value.toLowerCase();return e.value.filter(e=>e.name.toLowerCase().includes(t)||e.description.toLowerCase().includes(t)||e.categories&&e.categories.name.toLowerCase().includes(t))});async function u(){if(!t.value){t.value=!0,s.value=null;try{const{data:t,error:s}=await Gi.from("tools").select("\n          *,\n          categories ( * ),\n          tool_tags ( tags ( * ) )\n        ").eq("status","active").order("sort_order",{ascending:!0});if(s)throw s;const n=(null==t?void 0:t.map(e=>{var t;return{...e,tags:(null==(t=e.tool_tags)?void 0:t.map(e=>e.tags.name))||[]}}))||[];e.value=n,r.value=!0}catch(n){console.error("获取工具列表失败:",n),s.value=n}finally{t.value=!1}}}return{tools:e,loading:t,error:s,initialized:r,searchQuery:n,selectedCategory:i,sidebarCollapsed:o,filteredTools:c,fetchTools:u,initialize:async function(){r.value||await u()},toggleSidebar:function(){o.value=!o.value},clearError:function(){s.value=null},setSearchQuery:function(e){n.value=e},setSelectedCategory:function(e){i.value=e},incrementClickCount:async function(t){try{const{error:s}=await Gi.rpc("increment_click_count",{tool_id:t});if(s)return void console.error("增加点击次数失败:",s);const r=e.value.find(e=>e.id===t);r&&(r.click_count=(r.click_count||0)+1)}catch(s){console.error("增加点击次数失败:",s)}},toggleFavorite:async function(e){try{console.log("切换收藏:",e)}catch(t){console.error("切换收藏失败:",t)}}}}),Yi=p("auth",()=>{const e=l(null),t=l(!1),s=l(!1),r=l(null),n=a(()=>!!e.value),i=a(()=>{var t,s;return"admin"===(null==(t=e.value)?void 0:t.role)||"super_admin"===(null==(s=e.value)?void 0:s.role)});return{user:e,loading:t,initialized:s,error:r,isAuthenticated:n,isAdmin:i,initialize:function(){s.value||(t.value=!0,Gi.auth.onAuthStateChange(async(r,n)=>{if(null==n?void 0:n.user){const t=await async function(e){const{data:t,error:s}=await Gi.from("user_profiles").select("*").eq("id",e).single();return s?(console.error("获取用户资料失败:",s),null):t}(n.user.id);e.value=t?{...n.user,username:t.username||"未设置用户名",avatar_url:t.avatar_url||"",role:t.role||"user"}:{...n.user,username:n.user.email||"新用户",avatar_url:"",role:"user"}}else e.value=null;s.value=!0,t.value=!1}))},logout:async function(){t.value=!0,r.value=null;try{const{error:e}=await Gi.auth.signOut();if(e)throw e}catch(e){r.value=e,console.error("退出登录失败:",e)}finally{t.value=!1}},clearError:function(){r.value=null}}}),Zi=Object.freeze(Object.defineProperty({__proto__:null,useAuthStore:Yi},Symbol.toStringTag,{value:"Module"}));function Xi(e){return!!v()&&(g(e),!0)}function ea(e){return"function"==typeof e?e():n(e)}const ta="undefined"!=typeof window&&"undefined"!=typeof document;"undefined"!=typeof WorkerGlobalScope&&(globalThis,WorkerGlobalScope);const sa=Object.prototype.toString,ra=()=>{};function na(e,t){return function(...s){return new Promise((r,n)=>{Promise.resolve(e(()=>t.apply(this,s),{fn:t,thisArg:this,args:s})).then(r).catch(n)})}}const ia=e=>e();function aa(e,t=200,s={}){return na(function(e,t={}){let s,r,n=ra;const i=e=>{clearTimeout(e),n(),n=ra};return a=>{const o=ea(e),l=ea(t.maxWait);return s&&i(s),o<=0||void 0!==l&&l<=0?(r&&(i(r),r=null),Promise.resolve(a())):new Promise((e,c)=>{n=t.rejectOnCancel?c:e,l&&!r&&(r=setTimeout(()=>{s&&i(s),r=null,e(a())},l)),s=setTimeout(()=>{r&&i(r),r=null,e(a())},o)})}}(t,s),e)}function oa(e,t,s={}){const{eventFilter:r,...n}=s,{eventFilter:i,pause:a,resume:o,isActive:u}=function(e=ia){const t=l(!0);return{isActive:y(t),pause:function(){t.value=!1},resume:function(){t.value=!0},eventFilter:(...s)=>{t.value&&e(...s)}}}(r),d=function(e,t,s={}){const{eventFilter:r=ia,...n}=s;return c(e,na(r,t),n)}(e,t,{...n,eventFilter:i});return{stop:d,pause:a,resume:o,isActive:u}}function la(e,t=!0,s){b()?m(e,s):t?e():d(e)}function ca(e){var t;const s=ea(e);return null!=(t=null==s?void 0:s.$el)?t:s}const ua=ta?window:void 0;function da(...e){let t,s,r,n;if("string"==typeof e[0]||Array.isArray(e[0])?([s,r,n]=e,t=ua):[t,s,r,n]=e,!t)return ra;Array.isArray(s)||(s=[s]),Array.isArray(r)||(r=[r]);const i=[],a=()=>{i.forEach(e=>e()),i.length=0},o=c(()=>[ca(t),ea(n)],([e,t])=>{if(a(),!e)return;const n=(o=t,"[object Object]"===sa.call(o)?{...t}:t);var o;i.push(...s.flatMap(t=>r.map(s=>((e,t,s,r)=>(e.addEventListener(t,s,r),()=>e.removeEventListener(t,s,r)))(e,t,s,n))))},{immediate:!0,flush:"post"}),l=()=>{o(),a()};return Xi(l),l}function ha(e){const t=function(){const e=l(!1),t=b();return t&&m(()=>{e.value=!0},t),e}();return a(()=>(t.value,Boolean(e())))}function fa(e,t={}){const{window:s=ua}=t,r=ha(()=>s&&"matchMedia"in s&&"function"==typeof s.matchMedia);let n;const i=l(!1),a=e=>{i.value=e.matches},o=()=>{n&&("removeEventListener"in n?n.removeEventListener("change",a):n.removeListener(a))},c=_(()=>{r.value&&(o(),n=s.matchMedia(ea(e)),"addEventListener"in n?n.addEventListener("change",a):n.addListener(a),i.value=n.matches)});return Xi(()=>{c(),o(),n=void 0}),i}const pa="undefined"!=typeof globalThis?globalThis:"undefined"!=typeof window?window:"undefined"!=typeof global?global:"undefined"!=typeof self?self:{},ma="__vueuse_ssr_handlers__",va=ga();function ga(){return ma in pa||(pa[ma]=pa[ma]||{}),pa[ma]}const ya={boolean:{read:e=>"true"===e,write:e=>String(e)},object:{read:e=>JSON.parse(e),write:e=>JSON.stringify(e)},number:{read:e=>Number.parseFloat(e),write:e=>String(e)},any:{read:e=>e,write:e=>String(e)},string:{read:e=>e,write:e=>String(e)},map:{read:e=>new Map(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e.entries()))},set:{read:e=>new Set(JSON.parse(e)),write:e=>JSON.stringify(Array.from(e))},date:{read:e=>new Date(e),write:e=>e.toISOString()}},ba="vueuse-storage";function _a(e,t,r,n={}){var i;const{flush:a="pre",deep:o=!0,listenToStorageChanges:c=!0,writeDefaults:u=!0,mergeDefaults:h=!1,shallow:f,window:p=ua,eventFilter:m,onError:v=e=>{console.error(e)},initOnMounted:g}=n,y=(f?s:l)("function"==typeof t?t():t);if(!r)try{r=function(e,t){return va[e]||t}("getDefaultStorage",()=>{var e;return null==(e=ua)?void 0:e.localStorage})()}catch(O){v(O)}if(!r)return y;const b=ea(t),_=function(e){return null==e?"any":e instanceof Set?"set":e instanceof Map?"map":e instanceof Date?"date":"boolean"==typeof e?"boolean":"string"==typeof e?"string":"object"==typeof e?"object":Number.isNaN(e)?"any":"number"}(b),w=null!=(i=n.serializer)?i:ya[_],{pause:k,resume:S}=oa(y,()=>function(t){try{const s=r.getItem(e);if(null==t)E(s,null),r.removeItem(e);else{const n=w.write(t);s!==n&&(r.setItem(e,n),E(s,n))}}catch(O){v(O)}}(y.value),{flush:a,deep:o,eventFilter:m});function E(t,s){p&&p.dispatchEvent(new CustomEvent(ba,{detail:{key:e,oldValue:t,newValue:s,storageArea:r}}))}function T(t){if(!t||t.storageArea===r)if(t&&null==t.key)y.value=b;else if(!t||t.key===e){k();try{(null==t?void 0:t.newValue)!==w.write(y.value)&&(y.value=function(t){const s=t?t.newValue:r.getItem(e);if(null==s)return u&&null!=b&&r.setItem(e,w.write(b)),b;if(!t&&h){const e=w.read(s);return"function"==typeof h?h(e,b):"object"!==_||Array.isArray(e)?e:{...b,...e}}return"string"!=typeof s?s:w.read(s)}(t))}catch(O){v(O)}finally{t?d(S):S()}}}function j(e){T(e.detail)}return p&&c&&la(()=>{da(p,"storage",T),da(p,ba,j),g&&T()}),g||T(),y}const wa={mode:"auto",primaryColor:"#3b82f6",accentColor:"#10b981",borderRadius:"medium",fontSize:"medium",fontFamily:"system",compactMode:!1,highContrast:!1,reducedMotion:!1},ka=[{id:"default",name:"默认主题",description:"经典的蓝色主题，适合大多数用户",config:{primaryColor:"#3b82f6",accentColor:"#10b981"},preview:{primary:"#3b82f6",secondary:"#10b981",background:"#ffffff",surface:"#f8fafc"}},{id:"purple",name:"紫色主题",description:"优雅的紫色配色方案",config:{primaryColor:"#8b5cf6",accentColor:"#f59e0b"},preview:{primary:"#8b5cf6",secondary:"#f59e0b",background:"#ffffff",surface:"#faf5ff"}},{id:"green",name:"绿色主题",description:"清新的绿色主题，护眼舒适",config:{primaryColor:"#059669",accentColor:"#dc2626"},preview:{primary:"#059669",secondary:"#dc2626",background:"#ffffff",surface:"#f0fdf4"}},{id:"orange",name:"橙色主题",description:"温暖的橙色主题，充满活力",config:{primaryColor:"#ea580c",accentColor:"#7c3aed"},preview:{primary:"#ea580c",secondary:"#7c3aed",background:"#ffffff",surface:"#fff7ed"}},{id:"dark-blue",name:"深蓝主题",description:"专业的深蓝色主题",config:{mode:"dark",primaryColor:"#60a5fa",accentColor:"#34d399"},preview:{primary:"#60a5fa",secondary:"#34d399",background:"#0f172a",surface:"#1e293b"}},{id:"high-contrast",name:"高对比度",description:"高对比度主题，提升可访问性",config:{primaryColor:"#000000",accentColor:"#ffffff",highContrast:!0,borderRadius:"none"},preview:{primary:"#000000",secondary:"#ffffff",background:"#ffffff",surface:"#f5f5f5"}}];function Sa(){const e=function(e){const t=fa("(prefers-color-scheme: light)",e),s=fa("(prefers-color-scheme: dark)",e);return a(()=>s.value?"dark":t.value?"light":"no-preference")}(),t=_a("theme-config",wa),s=a(()=>"auto"===t.value.mode?"dark"===e.value?"dark":"light":"dark"===t.value.mode?"dark":"light"),r=a(()=>"dark"===s.value),n=a(()=>{const e=t.value,r=s.value,n=e.primaryColor,i=e.accentColor,a={"--color-primary":n,"--color-primary-rgb":l(n),"--color-accent":i,"--color-accent-rgb":l(i),"--border-radius-sm":u(e.borderRadius,"sm"),"--border-radius-md":u(e.borderRadius,"md"),"--border-radius-lg":u(e.borderRadius,"lg"),"--border-radius-xl":u(e.borderRadius,"xl"),"--font-size-xs":d(e.fontSize,"xs"),"--font-size-sm":d(e.fontSize,"sm"),"--font-size-base":d(e.fontSize,"base"),"--font-size-lg":d(e.fontSize,"lg"),"--font-size-xl":d(e.fontSize,"xl"),"--font-size-2xl":d(e.fontSize,"2xl"),"--font-size-3xl":d(e.fontSize,"3xl"),"--font-family":h(e.fontFamily),"--spacing-scale":e.compactMode?"0.875":"1","--transition-duration":e.reducedMotion?"0ms":"150ms","--animation-duration":e.reducedMotion?"0ms":"300ms"};return"dark"===r?Object.assign(a,{"--color-background":e.highContrast?"#000000":"#0f172a","--color-surface":e.highContrast?"#1a1a1a":"#1e293b","--color-surface-hover":e.highContrast?"#333333":"#334155","--color-border":e.highContrast?"#666666":"#475569","--color-text":e.highContrast?"#ffffff":"#f1f5f9","--color-text-secondary":e.highContrast?"#cccccc":"#cbd5e1","--color-text-muted":e.highContrast?"#999999":"#94a3b8"}):Object.assign(a,{"--color-background":(e.highContrast,"#ffffff"),"--color-surface":e.highContrast?"#f5f5f5":"#f8fafc","--color-surface-hover":e.highContrast?"#e5e5e5":"#f1f5f9","--color-border":e.highContrast?"#333333":"#e2e8f0","--color-text":e.highContrast?"#000000":"#1e293b","--color-text-secondary":e.highContrast?"#333333":"#475569","--color-text-muted":e.highContrast?"#666666":"#64748b"}),a}),i=()=>{const e=document.documentElement;e.setAttribute("data-theme",s.value),e.style.colorScheme=s.value,Object.entries(n.value).forEach(([t,s])=>{e.style.setProperty(t,s)}),e.classList.toggle("theme-dark",r.value),e.classList.toggle("theme-light",!r.value),e.classList.toggle("theme-compact",t.value.compactMode),e.classList.toggle("theme-high-contrast",t.value.highContrast),e.classList.toggle("theme-reduced-motion",t.value.reducedMotion)},o=e=>{t.value.mode=e},l=e=>{const t=/^#?([a-f\d]{2})([a-f\d]{2})([a-f\d]{2})$/i.exec(e);if(t){return`${parseInt(t[1],16)}, ${parseInt(t[2],16)}, ${parseInt(t[3],16)}`}return"0, 0, 0"},u=(e,t)=>({none:{sm:"0",md:"0",lg:"0",xl:"0"},small:{sm:"2px",md:"4px",lg:"6px",xl:"8px"},medium:{sm:"4px",md:"6px",lg:"8px",xl:"12px"},large:{sm:"6px",md:"8px",lg:"12px",xl:"16px"}}[e][t]||"4px"),d=(e,t)=>({small:{xs:"0.6875rem",sm:"0.75rem",base:"0.8125rem",lg:"0.9375rem",xl:"1.0625rem","2xl":"1.3125rem","3xl":"1.6875rem"},medium:{xs:"0.75rem",sm:"0.875rem",base:"1rem",lg:"1.125rem",xl:"1.25rem","2xl":"1.5rem","3xl":"1.875rem"},large:{xs:"0.8125rem",sm:"0.9375rem",base:"1.0625rem",lg:"1.1875rem",xl:"1.3125rem","2xl":"1.5625rem","3xl":"1.9375rem"}}[e][t]||"1rem"),h=e=>{const t={system:'-apple-system, BlinkMacSystemFont, "Segoe UI", Roboto, sans-serif',inter:'"Inter", -apple-system, BlinkMacSystemFont, sans-serif',roboto:'"Roboto", -apple-system, BlinkMacSystemFont, sans-serif',poppins:'"Poppins", -apple-system, BlinkMacSystemFont, sans-serif'};return t[e]||t.system},f=e=>"object"==typeof e&&null!==e&&["light","dark","auto"].includes(e.mode)&&"string"==typeof e.primaryColor&&"string"==typeof e.accentColor;return c([t,e],i,{deep:!0,immediate:!1}),m(()=>{i()}),{themeConfig:t,activeColorScheme:s,isDark:r,cssVariables:n,themePresets:ka,setThemeMode:o,toggleDark:()=>{"auto"===t.value.mode?o("dark"===e.value?"light":"dark"):o("dark"===t.value.mode?"light":"dark")},setPrimaryColor:e=>{t.value.primaryColor=e},setAccentColor:e=>{t.value.accentColor=e},applyPreset:e=>{const s=ka.find(t=>t.id===e);s&&Object.assign(t.value,s.config)},resetTheme:()=>{t.value={...wa}},exportTheme:()=>JSON.stringify(t.value,null,2),importTheme:e=>{try{const s=JSON.parse(e);return!!f(s)&&(t.value={...wa,...s},!0)}catch{return!1}},getThemeInfo:()=>({mode:t.value.mode,colorScheme:s.value,isDark:r.value,config:{...t.value}}),applyTheme:i}}const Ea={class:"theme-selector"},Ta=["title"],ja={class:"panel-header"},Oa={class:"panel-content"},Ca={class:"setting-group"},Pa={class:"theme-mode-options"},Aa=["onClick"],xa={class:"setting-group"},Ia={class:"theme-presets"},Ra=["onClick"],$a={class:"preset-preview"},La={class:"preset-info"},Ua={class:"preset-name"},Da={class:"preset-description"},qa={class:"setting-group"},Na={class:"color-settings"},Ma={class:"color-setting"},Va={class:"color-input-wrapper"},Ba={class:"color-value"},Fa={class:"color-setting"},za={class:"color-input-wrapper"},Wa={class:"color-value"},Ja={class:"setting-group"},Ga={class:"advanced-settings"},Ka={class:"setting-row"},Ha={class:"setting-row"},Qa={class:"setting-row"},Ya={class:"setting-row"},Za={class:"setting-checkbox"},Xa={class:"setting-row"},eo={class:"setting-checkbox"},to={class:"setting-row"},so={class:"setting-checkbox"},ro={class:"panel-actions"},no={key:0,class:"export-import-section"},io={class:"export-section"},ao=["value"],oo={class:"import-section"},lo=["disabled"],co={key:0,class:"import-error"},uo=(e,t)=>{const s=e.__vccOpts||e;for(const[r,n]of t)s[r]=n;return s},ho=uo(r({__name:"ThemeSelector",setup(e){const{themeConfig:t,isDark:s,themePresets:r,setThemeMode:i,setPrimaryColor:a,setAccentColor:o,applyPreset:c,resetTheme:u,exportTheme:d,importTheme:h}=Sa(),f=l(!1),p=l(!1),m=l(""),v=l(""),g=l(!1),y=[{value:"light",label:"浅色",icon:T},{value:"dark",label:"深色",icon:j},{value:"auto",label:"自动",icon:M}],b=e=>e.config.primaryColor===t.value.primaryColor&&e.config.accentColor===t.value.accentColor,_=async()=>{try{await navigator.clipboard.writeText(d()),g.value=!0,setTimeout(()=>{g.value=!1},2e3)}catch(e){console.error("复制失败:",e)}},B=()=>{if(v.value="",!m.value.trim())return void(v.value="请输入主题配置");h(m.value)?(m.value="",p.value=!1,f.value=!1):v.value="主题配置格式错误"};return(e,l)=>(D(),w("div",Ea,[k("button",{onClick:l[0]||(l[0]=e=>f.value=!f.value),class:"theme-toggle-button",title:n(s)?"切换到浅色主题":"切换到深色主题"},[n(s)?(D(),E(n(T),{key:0,class:"icon"})):(D(),E(n(j),{key:1,class:"icon"}))],8,Ta),f.value?(D(),w("div",{key:0,class:"theme-panel",onClick:l[16]||(l[16]=O(()=>{},["stop"]))},[k("div",ja,[l[18]||(l[18]=k("h3",{class:"panel-title"},"主题设置",-1)),k("button",{onClick:l[1]||(l[1]=e=>f.value=!1),class:"close-button"},[C(n(P),{class:"icon"})])]),k("div",Oa,[k("div",Ca,[l[19]||(l[19]=k("label",{class:"setting-label"},"主题模式",-1)),k("div",Pa,[(D(),w(A,null,x(y,e=>k("button",{key:e.value,onClick:t=>n(i)(e.value),class:q(["mode-button",{active:n(t).mode===e.value}])},[(D(),E(N(e.icon),{class:"mode-icon"})),k("span",null,$(e.label),1)],10,Aa)),64))])]),k("div",xa,[l[20]||(l[20]=k("label",{class:"setting-label"},"预设主题",-1)),k("div",Ia,[(D(!0),w(A,null,x(n(r),e=>(D(),w("div",{key:e.id,onClick:t=>n(c)(e.id),class:q(["preset-card",{active:b(e)}])},[k("div",$a,[k("div",{class:"preview-color primary",style:V({backgroundColor:e.preview.primary})},null,4),k("div",{class:"preview-color secondary",style:V({backgroundColor:e.preview.secondary})},null,4),k("div",{class:"preview-color background",style:V({backgroundColor:e.preview.background})},null,4),k("div",{class:"preview-color surface",style:V({backgroundColor:e.preview.surface})},null,4)]),k("div",La,[k("h4",Ua,$(e.name),1),k("p",Da,$(e.description),1)])],10,Ra))),128))])]),k("div",qa,[l[23]||(l[23]=k("label",{class:"setting-label"},"自定义颜色",-1)),k("div",Na,[k("div",Ma,[l[21]||(l[21]=k("label",{class:"color-label"},"主色调",-1)),k("div",Va,[I(k("input",{"onUpdate:modelValue":l[2]||(l[2]=e=>n(t).primaryColor=e),type:"color",class:"color-input",onChange:l[3]||(l[3]=e=>n(a)(n(t).primaryColor))},null,544),[[R,n(t).primaryColor]]),k("span",Ba,$(n(t).primaryColor),1)])]),k("div",Fa,[l[22]||(l[22]=k("label",{class:"color-label"},"强调色",-1)),k("div",za,[I(k("input",{"onUpdate:modelValue":l[4]||(l[4]=e=>n(t).accentColor=e),type:"color",class:"color-input",onChange:l[5]||(l[5]=e=>n(o)(n(t).accentColor))},null,544),[[R,n(t).accentColor]]),k("span",Wa,$(n(t).accentColor),1)])])])]),k("div",Ja,[l[33]||(l[33]=k("label",{class:"setting-label"},"高级设置",-1)),k("div",Ga,[k("div",Ka,[l[25]||(l[25]=k("label",{class:"setting-item-label"},"边框圆角",-1)),I(k("select",{"onUpdate:modelValue":l[6]||(l[6]=e=>n(t).borderRadius=e),class:"setting-select"},l[24]||(l[24]=[k("option",{value:"none"},"无圆角",-1),k("option",{value:"small"},"小圆角",-1),k("option",{value:"medium"},"中等圆角",-1),k("option",{value:"large"},"大圆角",-1)]),512),[[L,n(t).borderRadius]])]),k("div",Ha,[l[27]||(l[27]=k("label",{class:"setting-item-label"},"字体大小",-1)),I(k("select",{"onUpdate:modelValue":l[7]||(l[7]=e=>n(t).fontSize=e),class:"setting-select"},l[26]||(l[26]=[k("option",{value:"small"},"小",-1),k("option",{value:"medium"},"中",-1),k("option",{value:"large"},"大",-1)]),512),[[L,n(t).fontSize]])]),k("div",Qa,[l[29]||(l[29]=k("label",{class:"setting-item-label"},"字体",-1)),I(k("select",{"onUpdate:modelValue":l[8]||(l[8]=e=>n(t).fontFamily=e),class:"setting-select"},l[28]||(l[28]=[k("option",{value:"system"},"系统字体",-1),k("option",{value:"inter"},"Inter",-1),k("option",{value:"roboto"},"Roboto",-1),k("option",{value:"poppins"},"Poppins",-1)]),512),[[L,n(t).fontFamily]])]),k("div",Ya,[k("label",Za,[I(k("input",{"onUpdate:modelValue":l[9]||(l[9]=e=>n(t).compactMode=e),type:"checkbox"},null,512),[[U,n(t).compactMode]]),l[30]||(l[30]=k("span",{class:"checkbox-label"},"紧凑模式",-1))])]),k("div",Xa,[k("label",eo,[I(k("input",{"onUpdate:modelValue":l[10]||(l[10]=e=>n(t).highContrast=e),type:"checkbox"},null,512),[[U,n(t).highContrast]]),l[31]||(l[31]=k("span",{class:"checkbox-label"},"高对比度",-1))])]),k("div",to,[k("label",so,[I(k("input",{"onUpdate:modelValue":l[11]||(l[11]=e=>n(t).reducedMotion=e),type:"checkbox"},null,512),[[U,n(t).reducedMotion]]),l[32]||(l[32]=k("span",{class:"checkbox-label"},"减少动画",-1))])])])]),k("div",ro,[k("button",{onClick:l[12]||(l[12]=(...e)=>n(u)&&n(u)(...e)),class:"action-button secondary"}," 重置主题 "),k("button",{onClick:l[13]||(l[13]=e=>p.value=!p.value),class:"action-button secondary"}," 导入/导出 ")]),p.value?(D(),w("div",no,[k("div",io,[l[34]||(l[34]=k("label",{class:"setting-label"},"导出主题",-1)),k("textarea",{value:n(d)(),readonly:"",class:"export-textarea",onClick:l[14]||(l[14]=e=>e.target.select())},null,8,ao),k("button",{onClick:_,class:"copy-button"},$(g.value?"已复制":"复制配置"),1)]),k("div",oo,[l[35]||(l[35]=k("label",{class:"setting-label"},"导入主题",-1)),I(k("textarea",{"onUpdate:modelValue":l[15]||(l[15]=e=>m.value=e),placeholder:"粘贴主题配置...",class:"import-textarea"},null,512),[[R,m.value]]),k("button",{onClick:B,disabled:!m.value.trim(),class:"import-button"}," 导入主题 ",8,lo),v.value?(D(),w("p",co,$(v.value),1)):S("",!0)])])):S("",!0)])])):S("",!0),f.value?(D(),w("div",{key:1,class:"panel-overlay",onClick:l[17]||(l[17]=e=>f.value=!1)})):S("",!0)]))}}),[["__scopeId","data-v-655bd5d1"]]);const fo=new class{constructor(){t(this,"eventSource")}async getUserNotifications(e,t={}){try{const{page:s=1,limit:r=20,type:n,unread_only:i,important_only:a}=t;let o=Gi.from("notifications").select("*",{count:"exact"}).eq("user_id",e);n&&(o=o.eq("type",n)),i&&(o=o.eq("is_read",!1)),a&&(o=o.eq("is_important",!0)),o=o.or("expires_at.is.null,expires_at.gt."+(new Date).toISOString()),o=o.order("is_important",{ascending:!1}).order("created_at",{ascending:!1}).range((s-1)*r,s*r-1);const{data:l,error:c,count:u}=await o;if(c)throw c;return{notifications:l||[],total:u||0,stats:await this.getNotificationStats(e)}}catch(s){throw console.error("获取通知失败:",s),s}}async getNotificationStats(e){try{const{data:t,error:s}=await Gi.from("notifications").select("type, is_read, is_important").eq("user_id",e).or("expires_at.is.null,expires_at.gt."+(new Date).toISOString());if(s)throw s;const r={total:(null==t?void 0:t.length)||0,unread:0,important:0,by_type:{}};return null==t||t.forEach(e=>{e.is_read||r.unread++,e.is_important&&r.important++;const t=e.type;r.by_type[t]=(r.by_type[t]||0)+1}),r}catch(t){throw console.error("获取通知统计失败:",t),t}}async createNotification(e){try{const{data:t,error:s}=await Gi.from("notifications").insert({...e,is_read:!1,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}).select().single();if(s)throw s;return t?(this.sendRealTimeNotification(t),t):null}catch(t){return console.error("创建通知失败:",t),null}}async createBulkNotifications(e){try{const t=e.map(e=>({...e,is_read:!1,created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()})),{data:s,error:r}=await Gi.from("notifications").insert(t).select();if(r)throw r;return null==s||s.forEach(e=>{this.sendRealTimeNotification(e)}),s||[]}catch(t){throw console.error("批量创建通知失败:",t),t}}async markAsRead(e,t){try{const{error:s}=await Gi.from("notifications").update({is_read:!0,updated_at:(new Date).toISOString()}).eq("id",e).eq("user_id",t);if(s)throw s}catch(s){throw console.error("标记通知已读失败:",s),s}}async markAllAsRead(e,t){try{let s=Gi.from("notifications").update({is_read:!0,updated_at:(new Date).toISOString()}).eq("user_id",e).eq("is_read",!1);t&&(s=s.eq("type",t));const{error:r}=await s;if(r)throw r}catch(s){throw console.error("批量标记已读失败:",s),s}}async deleteNotification(e,t){try{const{error:s}=await Gi.from("notifications").delete().eq("id",e).eq("user_id",t);if(s)throw s}catch(s){throw console.error("删除通知失败:",s),s}}async cleanupExpiredNotifications(){try{const{error:e}=await Gi.from("notifications").delete().lt("expires_at",(new Date).toISOString());if(e)throw e}catch(e){throw console.error("清理过期通知失败:",e),e}}async getUserPreferences(e){try{const{data:t,error:s}=await Gi.from("notification_preferences").select("*").eq("user_id",e).maybeSingle();return s?(console.error("获取通知偏好失败:",s),null):t||await this.createDefaultPreferences(e)}catch(t){return console.error("获取通知偏好失败:",t),null}}async createDefaultPreferences(e){try{const t={user_id:e,email_notifications:!0,push_notifications:!0,system_notifications:!0,product_notifications:!0,order_notifications:!0,marketing_notifications:!1,notification_frequency:"immediate",created_at:(new Date).toISOString(),updated_at:(new Date).toISOString()},{data:s,error:r}=await Gi.from("notification_preferences").insert(t).select().single();return r?(console.error("创建默认通知偏好失败:",r),null):s||null}catch(t){return console.error("创建默认通知偏好失败:",t),null}}async updatePreferences(e,t){try{const{data:s,error:r}=await Gi.from("notification_preferences").update({...t,updated_at:(new Date).toISOString()}).eq("user_id",e).select().single();if(r)throw r;return s}catch(s){throw console.error("更新通知偏好失败:",s),s}}sendRealTimeNotification(e){Gi.channel("notifications").send({type:"broadcast",event:"new_notification",payload:e})}subscribeToNotifications(e,t){const s=Gi.channel(`notifications:${e}`).on("postgres_changes",{event:"INSERT",schema:"public",table:"notifications",filter:`user_id=eq.${e}`},e=>{t(e.new)}).subscribe();return()=>{Gi.removeChannel(s)}}async requestNotificationPermission(){if(!("Notification"in window))throw new Error("此浏览器不支持桌面通知");if("granted"===Notification.permission)return"granted";if("denied"!==Notification.permission){return await Notification.requestPermission()}return Notification.permission}async showBrowserNotification(e,t={}){try{if("granted"===await this.requestNotificationPermission()){const s=new Notification(e,{icon:"/favicon.ico",badge:"/favicon.ico",...t});setTimeout(()=>{s.close()},5e3)}}catch(s){console.error("显示浏览器通知失败:",s)}}async sendSystemNotification(e,t,s,r={}){try{const n=e.map(e=>({user_id:e,type:r.type||"system",title:t,message:s,action_url:r.action_url,action_text:r.action_text,is_important:r.is_important||!1,expires_at:r.expires_at}));await this.createBulkNotifications(n)}catch(n){throw console.error("发送系统通知失败:",n),n}}},po={class:"notification-content"},mo={class:"notification-header"},vo={class:"notification-icon"},go={class:"notification-meta"},yo={class:"notification-title"},bo={class:"notification-time"},_o={class:"notification-actions"},wo={class:"notification-body"},ko={class:"notification-message"},So={key:0,class:"notification-action"},Eo={key:0,class:"unread-indicator"},To=uo(r({__name:"NotificationItem",props:{notification:{}},emits:["read","delete","action"],setup(e,{emit:t}){const s=e,r=t,i=()=>{switch(s.notification.type){case"success":return Y;case"warning":return Q;case"error":return H;case"system":return K;case"product":return G;case"order":return J;default:return W}},a=e=>{const t=new Date(e),s=(new Date).getTime()-t.getTime(),r=Math.floor(s/6e4),n=Math.floor(s/36e5),i=Math.floor(s/864e5);return r<1?"刚刚":r<60?`${r}分钟前`:n<24?`${n}小时前`:i<7?`${i}天前`:t.toLocaleDateString("zh-CN",{month:"short",day:"numeric",hour:"2-digit",minute:"2-digit"})},o=()=>{r("read",s.notification.id)},l=()=>{r("delete",s.notification.id)},c=()=>{r("action",s.notification)};return(e,t)=>(D(),w("div",{class:q(["notification-item",{"is-unread":!e.notification.is_read,"is-important":e.notification.is_important,[`type-${e.notification.type}`]:!0}])},[k("div",po,[k("div",mo,[k("div",vo,[(D(),E(N(i()),{class:"icon"}))]),k("div",go,[k("h4",yo,$(e.notification.title),1),k("span",bo,$(a(e.notification.created_at)),1)]),k("div",_o,[e.notification.is_read?S("",!0):(D(),w("button",{key:0,onClick:o,class:"action-button read-button",title:"标记为已读"},[C(n(B),{class:"icon"})])),k("button",{onClick:l,class:"action-button delete-button",title:"删除通知"},[C(n(P),{class:"icon"})])])]),k("div",wo,[k("p",ko,$(e.notification.message),1),e.notification.action_url?(D(),w("div",So,[k("button",{onClick:c,class:"action-link"},[F($(e.notification.action_text||"查看详情")+" ",1),C(n(z),{class:"icon"})])])):S("",!0)])]),e.notification.is_read?S("",!0):(D(),w("div",Eo))],2))}}),[["__scopeId","data-v-3b3f7e34"]]),jo={class:"notification-settings-modal"},Oo={class:"modal-content"},Co={class:"modal-header"},Po={class:"modal-body"},Ao={key:0,class:"loading-state"},xo={key:1,class:"settings-form"},Io={class:"setting-section"},Ro={class:"setting-grid"},$o={class:"setting-item"},Lo={class:"setting-label"},Uo={class:"setting-item"},Do={class:"setting-label"},qo={class:"setting-item"},No={class:"setting-label"},Mo={class:"setting-item"},Vo={class:"setting-label"},Bo={class:"setting-item"},Fo={class:"setting-label"},zo={class:"setting-item"},Wo={class:"setting-label"},Jo={class:"setting-section"},Go={class:"frequency-options"},Ko=["value"],Ho={class:"option-content"},Qo={class:"option-title"},Yo={class:"option-description"},Zo={class:"setting-section"},Xo={class:"quiet-hours"},el={class:"time-input-group"},tl={class:"time-input-group"},sl={class:"modal-footer"},rl=["disabled"],nl=uo(r({__name:"NotificationSettings",emits:["close","updated"],setup(e,{emit:t}){const s=t,r=Yi(),i=l(!0),a=l(!1),o=l({id:"",user_id:"",email_notifications:!0,push_notifications:!0,system_notifications:!0,product_notifications:!0,order_notifications:!0,marketing_notifications:!1,notification_frequency:"immediate",quiet_hours_start:"22:00",quiet_hours_end:"08:00",created_at:"",updated_at:""}),c=[{value:"immediate",label:"立即通知",description:"收到通知后立即推送"},{value:"daily",label:"每日汇总",description:"每天汇总一次发送"},{value:"weekly",label:"每周汇总",description:"每周汇总一次发送"},{value:"never",label:"从不",description:"不接收任何通知"}],u=async()=>{if(r.user&&!a.value)try{a.value=!0,await fo.updatePreferences(r.user.id,o.value),s("updated"),s("close")}catch(e){console.error("保存通知设置失败:",e)}finally{a.value=!1}};return m(()=>{(async()=>{if(r.user)try{i.value=!0;const e=await fo.getUserPreferences(r.user.id);e&&(o.value=e)}catch(e){console.error("加载通知偏好失败:",e)}finally{i.value=!1}})()}),(e,t)=>(D(),w("div",jo,[k("div",{class:"modal-overlay",onClick:t[0]||(t[0]=t=>e.$emit("close"))}),k("div",Oo,[k("div",Co,[t[12]||(t[12]=k("h2",{class:"modal-title"},"通知设置",-1)),k("button",{onClick:t[1]||(t[1]=t=>e.$emit("close")),class:"close-button"},[C(n(P),{class:"icon"})])]),k("div",Po,[i.value?(D(),w("div",Ao,t[13]||(t[13]=[k("div",{class:"loading-spinner"},null,-1),k("p",null,"加载设置中...",-1)]))):(D(),w("div",xo,[k("div",Io,[t[26]||(t[26]=k("h3",{class:"section-title"},"通知类型",-1)),k("div",Ro,[k("div",$o,[k("label",Lo,[I(k("input",{"onUpdate:modelValue":t[2]||(t[2]=e=>o.value.email_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,o.value.email_notifications]]),t[14]||(t[14]=k("span",{class:"checkbox-text"},"邮件通知",-1))]),t[15]||(t[15]=k("p",{class:"setting-description"},"通过邮件接收重要通知",-1))]),k("div",Uo,[k("label",Do,[I(k("input",{"onUpdate:modelValue":t[3]||(t[3]=e=>o.value.push_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,o.value.push_notifications]]),t[16]||(t[16]=k("span",{class:"checkbox-text"},"浏览器通知",-1))]),t[17]||(t[17]=k("p",{class:"setting-description"},"在浏览器中显示桌面通知",-1))]),k("div",qo,[k("label",No,[I(k("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>o.value.system_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,o.value.system_notifications]]),t[18]||(t[18]=k("span",{class:"checkbox-text"},"系统通知",-1))]),t[19]||(t[19]=k("p",{class:"setting-description"},"接收系统更新和维护通知",-1))]),k("div",Mo,[k("label",Vo,[I(k("input",{"onUpdate:modelValue":t[5]||(t[5]=e=>o.value.product_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,o.value.product_notifications]]),t[20]||(t[20]=k("span",{class:"checkbox-text"},"产品通知",-1))]),t[21]||(t[21]=k("p",{class:"setting-description"},"接收新产品和更新通知",-1))]),k("div",Bo,[k("label",Fo,[I(k("input",{"onUpdate:modelValue":t[6]||(t[6]=e=>o.value.order_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,o.value.order_notifications]]),t[22]||(t[22]=k("span",{class:"checkbox-text"},"订单通知",-1))]),t[23]||(t[23]=k("p",{class:"setting-description"},"接收订单状态更新通知",-1))]),k("div",zo,[k("label",Wo,[I(k("input",{"onUpdate:modelValue":t[7]||(t[7]=e=>o.value.marketing_notifications=e),type:"checkbox",class:"setting-checkbox"},null,512),[[U,o.value.marketing_notifications]]),t[24]||(t[24]=k("span",{class:"checkbox-text"},"营销通知",-1))]),t[25]||(t[25]=k("p",{class:"setting-description"},"接收促销和营销活动通知",-1))])])]),k("div",Jo,[t[27]||(t[27]=k("h3",{class:"section-title"},"通知频率",-1)),k("div",Go,[(D(),w(A,null,x(c,e=>k("label",{key:e.value,class:q(["frequency-option",{active:o.value.notification_frequency===e.value}])},[I(k("input",{"onUpdate:modelValue":t[8]||(t[8]=e=>o.value.notification_frequency=e),value:e.value,type:"radio",class:"frequency-radio"},null,8,Ko),[[Z,o.value.notification_frequency]]),k("div",Ho,[k("span",Qo,$(e.label),1),k("span",Yo,$(e.description),1)])],2)),64))])]),k("div",Zo,[t[31]||(t[31]=k("h3",{class:"section-title"},"免打扰时间",-1)),k("div",Xo,[k("div",el,[t[28]||(t[28]=k("label",{class:"time-label"},"开始时间",-1)),I(k("input",{"onUpdate:modelValue":t[9]||(t[9]=e=>o.value.quiet_hours_start=e),type:"time",class:"time-input"},null,512),[[R,o.value.quiet_hours_start]])]),t[30]||(t[30]=k("div",{class:"time-separator"},"-",-1)),k("div",tl,[t[29]||(t[29]=k("label",{class:"time-label"},"结束时间",-1)),I(k("input",{"onUpdate:modelValue":t[10]||(t[10]=e=>o.value.quiet_hours_end=e),type:"time",class:"time-input"},null,512),[[R,o.value.quiet_hours_end]])])]),t[32]||(t[32]=k("p",{class:"setting-description"},"在此时间段内不会收到通知",-1))])]))]),k("div",sl,[k("button",{onClick:t[11]||(t[11]=t=>e.$emit("close")),class:"button button-secondary"}," 取消 "),k("button",{onClick:u,disabled:a.value,class:"button button-primary"},$(a.value?"保存中...":"保存设置"),9,rl)])])]))}}),[["__scopeId","data-v-8365f081"]]),il={class:"notification-center"},al={key:0,class:"notification-badge"},ol={class:"panel-header"},ll={class:"panel-actions"},cl=["disabled"],ul={class:"notification-filters"},dl=["onClick"],hl={key:0,class:"filter-count"},fl={class:"notification-list"},pl={key:0,class:"loading-state"},ml={key:1,class:"empty-state"},vl={key:2,class:"notifications"},gl={key:0,class:"load-more"},yl=["disabled"],bl=r({__name:"NotificationCenter",setup(e){const t=Yi(),s=l(!1),r=l(!1),i=l(!1),o=l(!1),u=l(!1),d=l([]),h=l({total:0,unread:0,important:0,by_type:{}}),f=l(1),p=l(!1),v=l("all");let g=null;const y=a(()=>[{key:"all",label:"全部",count:h.value.total},{key:"unread",label:"未读",count:h.value.unread},{key:"important",label:"重要",count:h.value.important},{key:"system",label:"系统",count:h.value.by_type.system||0},{key:"product",label:"产品",count:h.value.by_type.product||0},{key:"order",label:"订单",count:h.value.by_type.order||0}]),b=a(()=>{switch(v.value){case"unread":return d.value.filter(e=>!e.is_read);case"important":return d.value.filter(e=>e.is_important);case"system":case"product":case"order":return d.value.filter(e=>e.type===v.value);default:return d.value}}),_=()=>{s.value=!s.value,s.value&&0===d.value.length&&j()},T=()=>{s.value=!1},j=async(e=!0)=>{if(t.user)try{e?(i.value=!0,f.value=1,d.value=[]):o.value=!0;const s={page:f.value,limit:20};"unread"===v.value?s.unread_only=!0:"important"===v.value?s.important_only=!0:["system","product","order"].includes(v.value)&&(s.type=v.value);const r=await fo.getUserNotifications(t.user.id,s);e?d.value=r.notifications:d.value.push(...r.notifications),h.value=r.stats,p.value=d.value.length<r.total}catch(s){console.error("加载通知失败:",s)}finally{i.value=!1,o.value=!1}},I=async()=>{p.value&&!o.value&&(f.value++,await j(!1))},R=async()=>{if(t.user&&!u.value)try{u.value=!0;const e=["system","product","order"].includes(v.value)?v.value:void 0;await fo.markAllAsRead(t.user.id,e),d.value.forEach(t=>{e&&t.type!==e||(t.is_read=!0)}),await j()}catch(e){console.error("标记全部已读失败:",e)}finally{u.value=!1}},L=async e=>{if(t.user)try{await fo.markAsRead(e,t.user.id);const s=d.value.find(t=>t.id===e);s&&(s.is_read=!0,h.value.unread=Math.max(0,h.value.unread-1))}catch(s){console.error("标记已读失败:",s)}},U=async e=>{if(t.user)try{await fo.deleteNotification(e,t.user.id);const s=d.value.findIndex(t=>t.id===e);if(-1!==s){const e=d.value[s];d.value.splice(s,1),h.value.total--,e.is_read||h.value.unread--,e.is_important&&h.value.important--;const t=h.value.by_type[e.type]||0;h.value.by_type[e.type]=Math.max(0,t-1)}}catch(s){console.error("删除通知失败:",s)}},N=e=>{e.action_url&&window.open(e.action_url,"_blank"),e.is_read||L(e.id)},M=()=>{r.value=!1},V=()=>{switch(v.value){case"unread":return"所有通知都已阅读";case"important":return"暂无重要通知";case"system":return"暂无系统通知";case"product":return"暂无产品通知";case"order":return"暂无订单通知";default:return"您还没有收到任何通知"}},B=()=>{t.user&&(g=fo.subscribeToNotifications(t.user.id,e=>{d.value.unshift(e),h.value.total++,h.value.unread++,e.is_important&&h.value.important++;const t=h.value.by_type[e.type]||0;h.value.by_type[e.type]=t+1,fo.showBrowserNotification(e.title,{body:e.message,tag:e.id,requireInteraction:e.is_important})}))};return m(()=>{B(),t.user&&fo.getNotificationStats(t.user.id).then(e=>{h.value=e}).catch(e=>{console.error("加载通知统计失败:",e)})}),X(()=>{g&&g()}),c(v,()=>{j()}),c(()=>t.user,e=>{e?(B(),s.value&&j()):(g&&(g(),g=null),d.value=[],h.value={total:0,unread:0,important:0,by_type:{}})}),(e,t)=>(D(),w("div",il,[k("div",{class:"notification-trigger",onClick:_},[k("button",{class:q(["notification-button",{"has-unread":h.value.unread>0}])},[C(n(K),{class:"icon"}),h.value.unread>0?(D(),w("span",al,$(h.value.unread>99?"99+":h.value.unread),1)):S("",!0)],2)]),s.value?(D(),w("div",{key:0,class:"notification-panel",onClick:t[1]||(t[1]=O(()=>{},["stop"]))},[k("div",ol,[t[3]||(t[3]=k("h3",{class:"panel-title"},"通知中心",-1)),k("div",ll,[h.value.unread>0?(D(),w("button",{key:0,onClick:R,class:"mark-all-read-button",disabled:u.value},$(u.value?"标记中...":"全部已读"),9,cl)):S("",!0),k("button",{onClick:t[0]||(t[0]=e=>r.value=!0),class:"settings-button"},[C(n(ee),{class:"icon"})]),k("button",{onClick:T,class:"close-button"},[C(n(P),{class:"icon"})])])]),k("div",ul,[(D(!0),w(A,null,x(y.value,e=>(D(),w("button",{key:e.key,onClick:t=>v.value=e.key,class:q(["filter-button",{active:v.value===e.key}])},[F($(e.label)+" ",1),e.count>0?(D(),w("span",hl,$(e.count),1)):S("",!0)],10,dl))),128))]),k("div",fl,[i.value?(D(),w("div",pl,t[4]||(t[4]=[k("div",{class:"loading-spinner"},null,-1),k("p",null,"加载通知中...",-1)]))):0===b.value.length?(D(),w("div",ml,[t[5]||(t[5]=k("div",{class:"empty-icon"},"🔔",-1)),t[6]||(t[6]=k("h4",null,"暂无通知",-1)),k("p",null,$(V()),1)])):(D(),w("div",vl,[(D(!0),w(A,null,x(b.value,e=>(D(),E(To,{key:e.id,notification:e,onRead:L,onDelete:U,onAction:N},null,8,["notification"]))),128)),p.value?(D(),w("div",gl,[k("button",{onClick:I,disabled:o.value,class:"load-more-button"},$(o.value?"加载中...":"加载更多"),9,yl)])):S("",!0)]))])])):S("",!0),r.value?(D(),E(nl,{key:1,onClose:t[2]||(t[2]=e=>r.value=!1),onUpdated:M})):S("",!0),s.value?(D(),w("div",{key:2,class:"panel-overlay",onClick:T})):S("",!0)]))}}),_l=uo(bl,[["__scopeId","data-v-b040a3a2"]]),wl={class:"app-header"},kl={class:"header-content"},Sl={class:"header-left"},El={class:"header-center"},Tl={class:"search-container"},jl={class:"search-actions"},Ol={class:"header-right"},Cl={key:0,class:"user-menu"},Pl=["src","alt"],Al={key:1,class:"avatar-placeholder"},xl={key:0,class:"user-dropdown"},Il={class:"user-info"},Rl={class:"user-name"},$l={class:"user-email"},Ll=uo(r({__name:"AppHeader",setup(e){const t=ls(),s=Qi(),r=Yi(),i=l(),a=l(!1),o=()=>{s.searchQuery.trim()&&t.push({name:"Tools",query:{search:s.searchQuery.trim()}})},c=()=>{var e;s.searchQuery="",null==(e=i.value)||e.focus()},u=()=>{a.value=!a.value},d=()=>{a.value=!1},h=async()=>{try{await r.logout(),d(),t.push("/")}catch(e){console.error("退出登录失败:",e)}},f=e=>{var t;(e.ctrlKey||e.metaKey)&&"k"===e.key&&(e.preventDefault(),null==(t=i.value)||t.focus()),"Escape"===e.key&&(a.value=!1)},p=e=>{e.target.closest(".user-menu")||(a.value=!1)};return m(()=>{document.addEventListener("keydown",f),document.addEventListener("click",p)}),X(()=>{document.removeEventListener("keydown",f),document.removeEventListener("click",p)}),(e,t)=>{var l,f,p,m,v;const g=se("router-link");return D(),w("header",wl,[k("div",kl,[k("div",Sl,[k("button",{class:q(["sidebar-toggle",{active:!n(s).sidebarCollapsed}]),onClick:t[0]||(t[0]=e=>n(s).toggleSidebar())},[C(n(te),{class:"icon"})],2),C(g,{to:"/",class:"app-title"},{default:re(()=>t[2]||(t[2]=[k("div",{class:"title-icon"},"🚀",-1),k("div",{class:"title-text"},[k("h1",null,"工具导航站"),k("span",null,"让工作更高效")],-1)])),_:1,__:[2]})]),k("div",El,[k("div",Tl,[C(n(ne),{class:"search-icon"}),I(k("input",{ref_key:"searchInput",ref:i,"onUpdate:modelValue":t[1]||(t[1]=e=>n(s).searchQuery=e),type:"text",placeholder:"搜索工具、分类或功能...",class:"search-input",onKeydown:[ie(o,["enter"]),ie(c,["esc"])]},null,544),[[R,n(s).searchQuery]]),k("div",jl,[n(s).searchQuery?(D(),w("button",{key:0,class:"clear-search",onClick:c},[C(n(P),{class:"icon"})])):S("",!0),t[3]||(t[3]=k("div",{class:"search-shortcut"},"Ctrl+K",-1))])])]),k("div",Ol,[C(ho),C(_l),n(r).isAuthenticated?(D(),w("div",Cl,[k("button",{class:"user-avatar",onClick:u},[(null==(l=n(r).user)?void 0:l.avatarUrl)?(D(),w("img",{key:0,src:n(r).user.avatarUrl,alt:n(r).user.username,class:"avatar-image"},null,8,Pl)):(D(),w("div",Al,$(null==(p=null==(f=n(r).user)?void 0:f.username)?void 0:p.charAt(0).toUpperCase()),1))]),a.value?(D(),w("div",xl,[k("div",Il,[k("div",Rl,$(null==(m=n(r).user)?void 0:m.username),1),k("div",$l,$(null==(v=n(r).user)?void 0:v.email),1)]),t[10]||(t[10]=k("div",{class:"menu-divider"},null,-1)),C(g,{to:"/user/profile",class:"menu-item",onClick:d},{default:re(()=>[C(n(oe),{class:"icon"}),t[4]||(t[4]=F(" 个人资料 "))]),_:1,__:[4]}),C(g,{to:"/user/favorites",class:"menu-item",onClick:d},{default:re(()=>[C(n(le),{class:"icon"}),t[5]||(t[5]=F(" 我的收藏 "))]),_:1,__:[5]}),C(g,{to:"/user/orders",class:"menu-item",onClick:d},{default:re(()=>[C(n(G),{class:"icon"}),t[6]||(t[6]=F(" 我的订单 "))]),_:1,__:[6]}),C(g,{to:"/upload-product",class:"menu-item",onClick:d},{default:re(()=>[C(n(ce),{class:"icon"}),t[7]||(t[7]=F(" 上传产品 "))]),_:1,__:[7]}),t[11]||(t[11]=k("div",{class:"menu-divider"},null,-1)),n(r).isAdmin?(D(),E(g,{key:0,to:"/admin",class:"menu-item",onClick:d},{default:re(()=>[C(n(ee),{class:"icon"}),t[8]||(t[8]=F(" 管理后台 "))]),_:1,__:[8]})):S("",!0),k("button",{class:"menu-item logout",onClick:h},[C(n(ae),{class:"icon"}),t[9]||(t[9]=F(" 退出登录 "))])])):S("",!0)])):(D(),E(g,{key:1,to:"/auth/login",class:"login-btn"},{default:re(()=>[C(n(oe),{class:"icon"}),t[12]||(t[12]=F(" 登录 "))]),_:1,__:[12]}))])])])}}}),[["__scopeId","data-v-f3951d6a"]]),Ul={class:"app-footer"},Dl={class:"footer-content"},ql={class:"footer-main"},Nl={class:"footer-section company-info"},Ml={class:"company-logo"},Vl={class:"logo-text"},Bl={class:"company-description"},Fl={class:"social-links"},zl=["href"],Wl=["href"],Jl=["href"],Gl=["href"],Kl={class:"footer-section"},Hl={class:"footer-links"},Ql={class:"footer-section"},Yl={class:"footer-links"},Zl={class:"footer-section contact-info"},Xl={key:0,class:"contact-item"},ec={key:1,class:"contact-item"},tc={class:"contact-item"},sc={class:"contact-item"},rc={class:"footer-bottom"},nc={class:"footer-bottom-content"},ic={class:"copyright"},ac={class:"footer-stats"},oc={class:"stat-item"},lc={class:"stat-number"},cc={class:"stat-item"},uc={class:"stat-number"},dc={class:"stat-item"},hc={class:"stat-number"},fc=uo(r({__name:"AppFooter",setup(e){const t=l({name:"工具导航站",contact:{email:"<EMAIL>",phone:"+86 138-0000-0000"}}),s=l({companyDescription:"专注于为用户提供优质的工具导航和产品展示服务，致力于提升工作效率，让每个人都能找到最适合的工具和产品。",copyright:"© 2024 工具导航站. 保留所有权利.",stats:{toolsCount:1e3,categoriesCount:50,usersCount:1e4},social:{wechat:"",weibo:"",github:"https://github.com/jiayuwee/advanced-tools-navigation",email:"<EMAIL>"}});return m(()=>{(()=>{try{const e=localStorage.getItem("siteConfig"),r=localStorage.getItem("footerConfig");e&&(t.value={...t.value,...JSON.parse(e)}),r&&(s.value={...s.value,...JSON.parse(r)})}catch(e){console.error("加载配置失败:",e)}})()}),(e,r)=>{const i=se("router-link");return D(),w("footer",Ul,[k("div",Dl,[k("div",ql,[k("div",Nl,[k("div",Ml,[r[1]||(r[1]=k("div",{class:"logo-icon"},"🚀",-1)),k("div",Vl,[k("h3",null,$(t.value.name),1),r[0]||(r[0]=k("p",null,"让工作更高效",-1))])]),k("p",Bl,$(s.value.companyDescription),1),k("div",Fl,[s.value.social.wechat?(D(),w("a",{key:0,href:s.value.social.wechat,class:"social-link",title:"微信"},[C(n(de),{class:"icon"})],8,zl)):S("",!0),s.value.social.weibo?(D(),w("a",{key:1,href:s.value.social.weibo,class:"social-link",title:"微博"},[C(n(he),{class:"icon"})],8,Wl)):S("",!0),s.value.social.github?(D(),w("a",{key:2,href:s.value.social.github,class:"social-link",title:"GitHub"},[C(n(fe),{class:"icon"})],8,Jl)):S("",!0),s.value.social.email?(D(),w("a",{key:3,href:`mailto:${s.value.social.email}`,class:"social-link",title:"邮箱"},[C(n(pe),{class:"icon"})],8,Gl)):S("",!0)])]),k("div",Kl,[r[7]||(r[7]=k("h4",null,"快速导航",-1)),k("ul",Hl,[k("li",null,[C(i,{to:"/"},{default:re(()=>r[2]||(r[2]=[F("首页")])),_:1,__:[2]})]),k("li",null,[C(i,{to:"/tools"},{default:re(()=>r[3]||(r[3]=[F("工具导航")])),_:1,__:[3]})]),k("li",null,[C(i,{to:"/products"},{default:re(()=>r[4]||(r[4]=[F("产品展示")])),_:1,__:[4]})]),k("li",null,[C(i,{to:"/user/favorites"},{default:re(()=>r[5]||(r[5]=[F("我的收藏")])),_:1,__:[5]})]),k("li",null,[C(i,{to:"/admin"},{default:re(()=>r[6]||(r[6]=[F("管理后台")])),_:1,__:[6]})])])]),r[18]||(r[18]=ue('<div class="footer-section" data-v-95f500f0><h4 data-v-95f500f0>产品服务</h4><ul class="footer-links" data-v-95f500f0><li data-v-95f500f0><a href="#" data-v-95f500f0>开发工具</a></li><li data-v-95f500f0><a href="#" data-v-95f500f0>设计工具</a></li><li data-v-95f500f0><a href="#" data-v-95f500f0>效率工具</a></li><li data-v-95f500f0><a href="#" data-v-95f500f0>AI工具</a></li><li data-v-95f500f0><a href="#" data-v-95f500f0>学习资源</a></li></ul></div>',1)),k("div",Ql,[r[14]||(r[14]=k("h4",null,"帮助支持",-1)),k("ul",Yl,[k("li",null,[C(i,{to:"/help"},{default:re(()=>r[8]||(r[8]=[F("帮助支持")])),_:1,__:[8]})]),k("li",null,[C(i,{to:"/user-guide"},{default:re(()=>r[9]||(r[9]=[F("使用指南")])),_:1,__:[9]})]),k("li",null,[C(i,{to:"/faq"},{default:re(()=>r[10]||(r[10]=[F("常见问题")])),_:1,__:[10]})]),k("li",null,[C(i,{to:"/feedback"},{default:re(()=>r[11]||(r[11]=[F("意见反馈")])),_:1,__:[11]})]),k("li",null,[C(i,{to:"/contact"},{default:re(()=>r[12]||(r[12]=[F("联系我们")])),_:1,__:[12]})]),k("li",null,[C(i,{to:"/terms"},{default:re(()=>r[13]||(r[13]=[F("服务条款")])),_:1,__:[13]})])])]),k("div",Zl,[r[17]||(r[17]=k("h4",null,"联系我们",-1)),t.value.contact.phone?(D(),w("div",Xl,[C(n(me),{class:"contact-icon"}),k("span",null,$(t.value.contact.phone),1)])):S("",!0),t.value.contact.email?(D(),w("div",ec,[C(n(pe),{class:"contact-icon"}),k("span",null,$(t.value.contact.email),1)])):S("",!0),k("div",tc,[C(n(ve),{class:"contact-icon"}),r[15]||(r[15]=k("span",null,"北京市朝阳区科技园区",-1))]),k("div",sc,[C(n(ge),{class:"contact-icon"}),r[16]||(r[16]=k("span",null,"工作时间：9:00-18:00",-1))])])]),k("div",rc,[k("div",nc,[k("div",ic,[k("p",null,$(s.value.copyright),1),k("p",null,[C(i,{to:"/privacy-policy"},{default:re(()=>r[19]||(r[19]=[F("隐私政策")])),_:1,__:[19]}),r[22]||(r[22]=F(" | ")),C(i,{to:"/terms"},{default:re(()=>r[20]||(r[20]=[F("服务条款")])),_:1,__:[20]}),r[23]||(r[23]=F(" | ")),C(i,{to:"/sitemap"},{default:re(()=>r[21]||(r[21]=[F("网站地图")])),_:1,__:[21]})])]),k("div",ac,[k("div",oc,[k("span",lc,$(s.value.stats.toolsCount)+"+",1),r[24]||(r[24]=k("span",{class:"stat-label"},"精选工具",-1))]),k("div",cc,[k("span",uc,$(s.value.stats.categoriesCount)+"+",1),r[25]||(r[25]=k("span",{class:"stat-label"},"工具分类",-1))]),k("div",dc,[k("span",hc,$(s.value.stats.usersCount)+"+",1),r[26]||(r[26]=k("span",{class:"stat-label"},"用户使用",-1))])])])])])])}}}),[["__scopeId","data-v-95f500f0"]]),pc={class:"feedback-widget"},mc={key:0,class:"feedback-badge"},vc={key:1,class:"feedback-panel"},gc={class:"panel-header"},yc={class:"panel-tabs"},bc=["onClick"],_c={class:"panel-content"},wc={key:0,class:"submit-feedback"},kc={class:"form-group"},Sc={class:"form-group"},Ec={class:"form-group"},Tc={class:"form-group"},jc={class:"form-group"},Oc={class:"form-label"},Cc={class:"form-actions"},Pc=["disabled"],Ac={key:1,class:"feedback-history"},xc={key:0,class:"loading-state"},Ic={key:1,class:"empty-state"},Rc={key:2,class:"feedback-list"},$c={class:"feedback-header"},Lc={class:"feedback-date"},Uc={class:"feedback-title"},Dc={class:"feedback-content"},qc={key:0,class:"feedback-response"},Nc={class:"response-date"},Mc={key:2,class:"feedback-stats"},Vc={class:"stats-grid"},Bc={class:"stat-card"},Fc={class:"stat-number"},zc={class:"stat-card"},Wc={class:"stat-number"},Jc={class:"stat-card"},Gc={class:"stat-number"},Kc={class:"stat-card"},Hc={class:"stat-number"},Qc={class:"stats-charts"},Yc={class:"chart-section"},Zc={class:"type-distribution"},Xc={class:"type-label"},eu={class:"type-bar"},tu={class:"type-count"},su={key:2,class:"success-message"},ru=uo(r({__name:"FeedbackWidget",setup(e){const t=Yi(),s=l(!1),r=l("submit"),i=l(!1),o=l(!1),c=l(!1),u=f({type:"",title:"",content:"",priority:"medium",includeSystemInfo:!0}),d=l([]),h=l({total:0,pending:0,resolved:0,response_rate:0,by_type:{}}),p=[{id:"submit",label:"提交反馈"},{id:"history",label:"反馈历史"},{id:"stats",label:"反馈统计"}],v=a(()=>d.value.filter(e=>!e.is_read).length),g=()=>{s.value=!s.value,s.value&&"history"===r.value&&b()},y=async()=>{if(t.user)try{o.value=!0;const e={...u,user_id:t.user.id,system_info:u.includeSystemInfo?_():null};console.log("提交反馈:",e),await new Promise(e=>setTimeout(e,1e3)),Object.assign(u,{type:"",title:"",content:"",priority:"medium",includeSystemInfo:!0}),c.value=!0,setTimeout(()=>{c.value=!1},3e3),s.value=!1}catch(e){console.error("提交反馈失败:",e),alert("提交反馈失败，请稍后重试")}finally{o.value=!1}else alert("请先登录后再提交反馈")},b=async()=>{if(t.user)try{i.value=!0,console.log("加载反馈历史"),d.value=[{id:"1",type:"feature",title:"希望增加暗色主题",content:"建议增加暗色主题选项，方便夜间使用",priority:"medium",status:"resolved",response:"感谢您的建议！暗色主题功能已经在最新版本中上线。",response_at:"2024-12-25T10:00:00Z",is_read:!0,created_at:"2024-12-24T15:30:00Z"}],h.value={total:5,pending:2,resolved:3,response_rate:80,by_type:{bug:2,feature:2,improvement:1}}}catch(e){console.error("加载反馈历史失败:",e)}finally{i.value=!1}},_=()=>({userAgent:navigator.userAgent,platform:navigator.platform,language:navigator.language,screenResolution:`${screen.width}x${screen.height}`,timestamp:(new Date).toISOString()}),E=e=>({bug:"Bug 报告",feature:"功能建议",improvement:"改进建议",question:"问题咨询",other:"其他"}[e]||e),T=e=>new Date(e).toLocaleString("zh-CN");return m(()=>{t.user&&b()}),(e,t)=>(D(),w("div",pc,[s.value?S("",!0):(D(),w("button",{key:0,onClick:g,class:q(["feedback-button",{"has-feedback":v.value>0}]),title:"用户反馈"},[C(n(de),{class:"icon"}),v.value>0?(D(),w("span",mc,$(v.value),1)):S("",!0)],2)),s.value?(D(),w("div",vc,[k("div",gc,[t[5]||(t[5]=k("h3",{class:"panel-title"},"用户反馈",-1)),k("button",{onClick:g,class:"close-button"},[C(n(P),{class:"icon"})])]),k("div",yc,[(D(),w(A,null,x(p,e=>k("button",{key:e.id,onClick:t=>r.value=e.id,class:q(["tab-button",{active:r.value===e.id}])},$(e.label),11,bc)),64))]),k("div",_c,["submit"===r.value?(D(),w("div",wc,[k("form",{onSubmit:O(y,["prevent"]),class:"feedback-form"},[k("div",kc,[t[7]||(t[7]=k("label",{for:"feedback-type",class:"form-label"},"反馈类型",-1)),I(k("select",{id:"feedback-type","onUpdate:modelValue":t[0]||(t[0]=e=>u.type=e),class:"form-select",required:""},t[6]||(t[6]=[ue('<option value="" data-v-5862e757>请选择反馈类型</option><option value="bug" data-v-5862e757>Bug 报告</option><option value="feature" data-v-5862e757>功能建议</option><option value="improvement" data-v-5862e757>改进建议</option><option value="question" data-v-5862e757>问题咨询</option><option value="other" data-v-5862e757>其他</option>',6)]),512),[[L,u.type]])]),k("div",Sc,[t[8]||(t[8]=k("label",{for:"feedback-title",class:"form-label"},"标题",-1)),I(k("input",{id:"feedback-title","onUpdate:modelValue":t[1]||(t[1]=e=>u.title=e),type:"text",class:"form-input",placeholder:"简要描述您的反馈",required:""},null,512),[[R,u.title]])]),k("div",Ec,[t[9]||(t[9]=k("label",{for:"feedback-content",class:"form-label"},"详细描述",-1)),I(k("textarea",{id:"feedback-content","onUpdate:modelValue":t[2]||(t[2]=e=>u.content=e),class:"form-textarea",rows:"4",placeholder:"请详细描述您的反馈内容",required:""},null,512),[[R,u.content]])]),k("div",Tc,[t[11]||(t[11]=k("label",{for:"feedback-priority",class:"form-label"},"优先级",-1)),I(k("select",{id:"feedback-priority","onUpdate:modelValue":t[3]||(t[3]=e=>u.priority=e),class:"form-select"},t[10]||(t[10]=[k("option",{value:"low"},"低",-1),k("option",{value:"medium"},"中",-1),k("option",{value:"high"},"高",-1),k("option",{value:"urgent"},"紧急",-1)]),512),[[L,u.priority]])]),k("div",jc,[k("label",Oc,[I(k("input",{"onUpdate:modelValue":t[4]||(t[4]=e=>u.includeSystemInfo=e),type:"checkbox",class:"form-checkbox"},null,512),[[U,u.includeSystemInfo]]),t[12]||(t[12]=F(" 包含系统信息（浏览器、设备等） "))])]),k("div",Cc,[k("button",{type:"submit",disabled:o.value,class:"submit-button"},$(o.value?"提交中...":"提交反馈"),9,Pc)])],32)])):"history"===r.value?(D(),w("div",Ac,[i.value?(D(),w("div",xc,t[13]||(t[13]=[k("div",{class:"loading-spinner"},null,-1),k("p",null,"加载反馈历史...",-1)]))):0===d.value.length?(D(),w("div",Ic,[C(n(de),{class:"empty-icon"}),t[14]||(t[14]=k("p",null,"暂无反馈记录",-1))])):(D(),w("div",Rc,[(D(!0),w(A,null,x(d.value,e=>{return D(),w("div",{key:e.id,class:q(["feedback-item",{unread:!e.is_read}])},[k("div",$c,[k("span",{class:q(["feedback-type",e.type])},$(E(e.type)),3),k("span",{class:q(["feedback-priority",e.priority])},$((s=e.priority,{low:"低",medium:"中",high:"高",urgent:"紧急"}[s]||s)),3),k("span",Lc,$(T(e.created_at)),1)]),k("h4",Uc,$(e.title),1),k("p",Dc,$(e.content),1),e.response?(D(),w("div",qc,[t[15]||(t[15]=k("h5",null,"开发者回复：",-1)),k("p",null,$(e.response),1),k("span",Nc,$(T(e.response_at)),1)])):S("",!0)],2);var s}),128))]))])):"stats"===r.value?(D(),w("div",Mc,[k("div",Vc,[k("div",Bc,[k("div",Fc,$(h.value.total),1),t[16]||(t[16]=k("div",{class:"stat-label"},"总反馈数",-1))]),k("div",zc,[k("div",Wc,$(h.value.pending),1),t[17]||(t[17]=k("div",{class:"stat-label"},"待处理",-1))]),k("div",Jc,[k("div",Gc,$(h.value.resolved),1),t[18]||(t[18]=k("div",{class:"stat-label"},"已解决",-1))]),k("div",Kc,[k("div",Hc,$(h.value.response_rate)+"%",1),t[19]||(t[19]=k("div",{class:"stat-label"},"回复率",-1))])]),k("div",Qc,[k("div",Yc,[t[20]||(t[20]=k("h4",null,"反馈类型分布",-1)),k("div",Zc,[(D(!0),w(A,null,x(h.value.by_type,(e,t)=>(D(),w("div",{key:t,class:"type-item"},[k("span",Xc,$(E(t)),1),k("div",eu,[k("div",{class:"type-fill",style:V({width:e/h.value.total*100+"%"})},null,4)]),k("span",tu,$(e),1)]))),128))])])])])):S("",!0)])])):S("",!0),c.value?(D(),w("div",su,[C(n(Y),{class:"success-icon"}),t[21]||(t[21]=k("span",null,"反馈提交成功！我们会尽快处理您的反馈。",-1))])):S("",!0)]))}}),[["__scopeId","data-v-5862e757"]]),nu={class:"status-bar"},iu={class:"status-content"},au={class:"status-left"},ou={class:"status-item"},lu={class:"status-text"},cu={class:"status-item"},uu={class:"status-text"},du={class:"status-item"},hu={class:"status-text"},fu={class:"status-center"},pu={class:"status-item"},mu={class:"status-text"},vu={class:"status-right"},gu={class:"status-item"},yu={class:"status-text"},bu={class:"status-item version"},_u={class:"status-text"},wu=uo(r({__name:"StatusBar",setup(e){const t=l(""),s=l("已连接"),r=l("connected"),i=l(128),a=l(1e3),o=l("在线"),c=l(!1),u=l("1.0.0");let d=null;const h=()=>{const e=new Date;t.value=e.toLocaleTimeString("zh-CN",{hour12:!1,hour:"2-digit",minute:"2-digit",second:"2-digit"})},f=()=>{navigator.onLine?(s.value="已连接",r.value="connected",o.value="在线"):(s.value="离线",r.value="disconnected",o.value="离线")},p=()=>{document.fullscreenElement?(document.exitFullscreen(),c.value=!1):(document.documentElement.requestFullscreen(),c.value=!0)},v=()=>{c.value=!!document.fullscreenElement},g=()=>{f()},y=()=>{f()};return m(()=>{h(),f(),d=setInterval(h,1e3),window.addEventListener("online",g),window.addEventListener("offline",y),document.addEventListener("fullscreenchange",v),setInterval(()=>{i.value=Math.floor(50*Math.random())+100},3e4)}),X(()=>{d&&clearInterval(d),window.removeEventListener("online",g),window.removeEventListener("offline",y),document.removeEventListener("fullscreenchange",v)}),(e,l)=>(D(),w("div",nu,[k("div",iu,[k("div",au,[k("div",ou,[C(n(ye),{class:"status-icon"}),k("span",lu,$(s.value),1),k("div",{class:q(["status-indicator",r.value])},null,2)]),k("div",cu,[C(n(be),{class:"status-icon"}),k("span",uu,$(i.value)+" 在线",1)]),k("div",du,[C(n(_e),{class:"status-icon"}),k("span",hu,$(a.value)+" 工具",1)])]),k("div",fu,[k("div",pu,[C(n(ge),{class:"status-icon"}),k("span",mu,$(t.value),1)])]),k("div",vu,[k("div",gu,[C(n(we),{class:"status-icon"}),k("span",yu,$(o.value),1)]),k("div",bu,[k("span",_u,"v"+$(u.value),1)]),k("button",{class:"status-btn",title:"全屏切换",onClick:p},[c.value?(D(),E(n(Se),{key:1,class:"status-icon"})):(D(),E(n(ke),{key:0,class:"status-icon"}))])])])]))}}),[["__scopeId","data-v-10333581"]]),ku={id:"app"},Su={key:0,class:"global-error"},Eu={class:"error-content"},Tu={class:"error-message"},ju=uo(r({__name:"App",setup(e){const t=l(null),s=()=>{t.value=null};return Ee((e,s,r)=>(console.error("全局错误:",e,r),t.value=e instanceof Error?e.message:"发生未知错误",!1)),m(()=>{window.addEventListener("error",e=>{var s;console.error("页面加载错误:",e.error),t.value=(null==(s=e.error)?void 0:s.message)||"页面加载失败"}),window.addEventListener("offline",()=>{t.value="网络连接已断开"})}),(e,r)=>{const n=se("RouterView");return D(),w("div",ku,[t.value?(D(),w("div",Su,[k("div",Eu,[k("span",Tu,$(t.value),1),k("button",{class:"error-close",onClick:s},"×")])])):S("",!0),C(Ll),C(n,null,{default:re(({Component:e})=>[C(Te,{name:"fade",mode:"out-in"},{default:re(()=>[(D(),E(N(e)))]),_:2},1024)]),_:1}),C(fc),C(ru),C(wu)])}}}),[["__scopeId","data-v-f10e280b"]]),Ou=[{path:"/",name:"Home",component:()=>ds(()=>import("./EnhancedHomeView-DdqcJPLZ.js"),__vite__mapDeps([0,1,2,3,4]),import.meta.url),meta:{title:"工具导航站",description:"高效的工具导航和产品展示平台"}},{path:"/search",name:"SearchResults",component:()=>ds(()=>import("./SearchResultsView-DqcTq4xp.js"),__vite__mapDeps([5,1,2,3,6]),import.meta.url),meta:{title:"搜索结果",description:"搜索工具和产品"}},{path:"/help",name:"HelpSupport",component:()=>ds(()=>import("./HelpSupportView-DWIQ7VAC.js"),__vite__mapDeps([7,1,8]),import.meta.url),meta:{title:"帮助支持",description:"获取使用帮助和技术支持"}},{path:"/faq",name:"FAQ",component:()=>ds(()=>import("./FAQView-BFRoM9xK.js"),__vite__mapDeps([9,1]),import.meta.url),meta:{title:"常见问题",description:"查看常见问题解答"}},{path:"/user-guide",name:"UserGuide",component:()=>ds(()=>import("./UserGuideView-DQU9NIhA.js"),__vite__mapDeps([10,1]),import.meta.url),meta:{title:"使用指南",description:"详细的使用说明和操作指南"}},{path:"/guide",redirect:"/user-guide"},{path:"/feedback",name:"Feedback",component:()=>ds(()=>import("./FeedbackView-Bw5b4gym.js"),__vite__mapDeps([11,1]),import.meta.url),meta:{title:"意见反馈",description:"提交您的意见和建议"}},{path:"/contact",name:"Contact",component:()=>ds(()=>import("./ContactView-CkEI2Syf.js"),__vite__mapDeps([12,1]),import.meta.url),meta:{title:"联系我们",description:"多种方式联系我们获取帮助"}},{path:"/terms",name:"TermsOfService",component:()=>ds(()=>import("./TermsOfServiceView-CXpvxheH.js"),__vite__mapDeps([13,1]),import.meta.url),meta:{title:"服务条款",description:"网站服务条款和使用协议"}},{path:"/upload-product",name:"ProductUpload",component:()=>ds(()=>import("./ProductUploadView-DRXY1Ej5.js"),__vite__mapDeps([14,1]),import.meta.url),meta:{title:"产品上传",description:"上传您的产品到平台",requiresAuth:!0}},{path:"/tools",name:"Tools",component:()=>ds(()=>import("./ToolsView-CTBVCbDK.js"),__vite__mapDeps([15,1,16]),import.meta.url),meta:{title:"工具导航",description:"发现和管理您的常用工具"}},{path:"/tools/:id",name:"ToolDetail",component:()=>ds(()=>import("./ToolDetailView-sM9OHp_b.js"),__vite__mapDeps([17,1,18]),import.meta.url),meta:{title:"工具详情",description:"查看工具详细信息和使用教程"}},{path:"/products",name:"Products",component:()=>ds(()=>import("./ProductsView-U3FaM-Hu.js"),__vite__mapDeps([19,1,20]),import.meta.url),meta:{title:"产品展示",description:"浏览和购买优质产品"}},{path:"/product/:id",name:"ProductDetail",component:()=>ds(()=>import("./ProductDetailView-izR7Is7_.js"),__vite__mapDeps([21,1,22]),import.meta.url),meta:{title:"产品详情",description:"查看产品详细信息"}},{path:"/user",name:"User",component:()=>ds(()=>import("./UserView-cTq0MBXA.js"),__vite__mapDeps([23,1,24]),import.meta.url),meta:{title:"个人中心",description:"管理您的账户和偏好设置",requiresAuth:!0},children:[{path:"profile",name:"UserProfile",component:()=>ds(()=>import("./ProfileView-37yX491w.js"),__vite__mapDeps([25,1,26]),import.meta.url),meta:{title:"个人资料",requiresAuth:!0}},{path:"favorites",name:"UserFavorites",component:()=>ds(()=>import("./FavoritesView-B8dHQ6IJ.js"),__vite__mapDeps([27,1,28]),import.meta.url),meta:{title:"我的收藏",requiresAuth:!0}},{path:"orders",name:"UserOrders",component:()=>ds(()=>import("./OrdersView-MKsWNrft.js"),__vite__mapDeps([29,1,30]),import.meta.url),meta:{title:"我的订单",requiresAuth:!0}}]},{path:"/auth",name:"Auth",component:()=>ds(()=>import("./AuthView-DRKopJy2.js"),__vite__mapDeps([31,1,32]),import.meta.url),meta:{title:"登录注册",description:"登录或注册您的账户"},children:[{path:"login",name:"Login",component:()=>ds(()=>import("./LoginView-DwHD2DlN.js"),__vite__mapDeps([33,1,34]),import.meta.url),meta:{title:"登录"}},{path:"register",name:"Register",component:()=>ds(()=>import("./RegisterView-DJqFKpGO.js"),__vite__mapDeps([35,1,36]),import.meta.url),meta:{title:"注册"}},{path:"forgot-password",name:"ForgotPassword",component:()=>ds(()=>import("./ForgotPasswordView-BsEquy06.js"),__vite__mapDeps([37,1,38]),import.meta.url),meta:{title:"忘记密码"}}]},{path:"/admin",name:"Admin",component:()=>ds(()=>import("./AdminView-BHanQEuE.js"),__vite__mapDeps([39,1,40]),import.meta.url),meta:{title:"管理后台",description:"系统管理和数据统计",requiresAuth:!0,requiresAdmin:!0},children:[{path:"",redirect:"/admin/dashboard"},{path:"dashboard",name:"AdminDashboard",component:()=>ds(()=>import("./DashboardView-BK5pYTOp.js"),__vite__mapDeps([41,1,42]),import.meta.url),meta:{title:"仪表盘",requiresAuth:!0,requiresAdmin:!0}},{path:"tools",name:"AdminTools",component:()=>ds(()=>import("./AdminToolsView-CVgUrrhD.js"),__vite__mapDeps([43,1,44]),import.meta.url),meta:{title:"工具管理",requiresAuth:!0,requiresAdmin:!0}},{path:"products",name:"AdminProducts",component:()=>ds(()=>import("./ProductManagementView-BOjebqFo.js"),__vite__mapDeps([45,1]),import.meta.url),meta:{title:"产品管理",requiresAuth:!0,requiresAdmin:!0}},{path:"settings",name:"AdminSettings",component:()=>ds(()=>import("./SettingsView-_VYKdPFi.js"),__vite__mapDeps([46,1,47]),import.meta.url),meta:{title:"系统设置",requiresAuth:!0,requiresAdmin:!0}}]},{path:"/payment",name:"Payment",component:()=>ds(()=>import("./PaymentView-CW7EphiN.js"),__vite__mapDeps([48,1,49]),import.meta.url),meta:{title:"支付页面",description:"安全的支付处理",requiresAuth:!0}},{path:"/payment/success",name:"PaymentSuccess",component:()=>ds(()=>import("./PaymentSuccessView-BieT_M2W.js"),__vite__mapDeps([50,1,51]),import.meta.url),meta:{title:"支付成功",description:"支付完成确认"}},{path:"/payment/cancel",name:"PaymentCancel",component:()=>ds(()=>import("./PaymentCancelView-CzRWOnua.js"),__vite__mapDeps([52,1,53]),import.meta.url),meta:{title:"支付取消",description:"支付已取消"}},{path:"/settings",name:"SimpleSettings",component:()=>ds(()=>import("./SimpleSettingsView-COGravRk.js"),__vite__mapDeps([54,1,55]),import.meta.url),meta:{title:"网站设置",description:"更新网站内容和配置"}},{path:"/test-settings",name:"TestSettings",component:()=>ds(()=>import("./TestSettingsView-DuhMWWdo.js"),__vite__mapDeps([56,1,57]),import.meta.url),meta:{title:"测试设置",description:"测试设置页面"}},{path:"/:pathMatch(.*)*",name:"NotFound",component:()=>ds(()=>import("./NotFoundView-C9ngCMrI.js"),__vite__mapDeps([58,1,59]),import.meta.url),meta:{title:"页面未找到",description:"您访问的页面不存在"}}],Cu=function(e){const t=Ut(e.routes,e),r=e.parseQuery||zt,a=e.stringifyQuery||Wt,o=e.history,l=Zt(),c=Zt(),u=Zt(),h=s(at);let f=at;Ce&&e.scrollBehavior&&"scrollRestoration"in history&&(history.scrollRestoration="manual");const p=xe.bind(null,e=>""+e),m=xe.bind(null,Ye),v=xe.bind(null,Ze);function g(e,s){if(s=Ae({},s||h.value),"string"==typeof e){const n=et(r,e,s.path),i=t.resolve({path:n.path},s),a=o.createHref(n.fullPath);return Ae(n,i,{params:v(i.params),hash:Ze(n.hash),redirectedFrom:void 0,href:a})}let n;if(null!=e.path)n=Ae({},e,{path:et(r,e.path,s.path).path});else{const t=Ae({},e.params);for(const e in t)null==t[e]&&delete t[e];n=Ae({},e,{params:m(t)}),s.params=m(s.params)}const i=t.resolve(n,s),l=e.hash||"";i.params=p(v(i.params));const c=function(e,t){const s=t.query?e(t.query):"";return t.path+(s&&"?")+s+(t.hash||"")}(a,Ae({},e,{hash:(u=l,Ke(u).replace(ze,"{").replace(Je,"}").replace(Be,"^")),path:i.path}));var u;const d=o.createHref(c);return Ae({fullPath:c,hash:l,query:a===Wt?Jt(e.query):e.query||{}},i,{redirectedFrom:void 0,href:d})}function y(e){return"string"==typeof e?et(r,e,h.value.path):Ae({},e)}function b(e,t){if(f!==e)return Tt(8,{from:t,to:e})}function _(e){return k(e)}function w(e){const t=e.matched[e.matched.length-1];if(t&&t.redirect){const{redirect:s}=t;let r="function"==typeof s?s(e):s;return"string"==typeof r&&(r=r.includes("?")||r.includes("#")?r=y(r):{path:r},r.params={}),Ae({query:e.query,hash:e.hash,params:null!=r.path?{}:e.params},r)}}function k(e,t){const s=f=g(e),r=h.value,n=e.state,i=e.force,o=!0===e.replace,l=w(s);if(l)return k(Ae(y(l),{state:"object"==typeof l?Ae({},n,l.state):n,force:i,replace:o}),t||s);const c=s;let u;return c.redirectedFrom=t,!i&&function(e,t,s){const r=t.matched.length-1,n=s.matched.length-1;return r>-1&&r===n&&st(t.matched[r],s.matched[n])&&rt(t.params,s.params)&&e(t.query)===e(s.query)&&t.hash===s.hash}(a,r,s)&&(u=Tt(16,{to:c,from:r}),L(r,r,!0,!1)),(u?Promise.resolve(u):T(c,r)).catch(e=>jt(e)?jt(e,2)?e:$(e):R(e,c,r)).then(e=>{if(e){if(jt(e,2))return k(Ae({replace:o},y(e.to),{state:"object"==typeof e.to?Ae({},n,e.to.state):n,force:i}),t||c)}else e=O(c,r,!0,o,n);return j(c,r,e),e})}function S(e,t){const s=b(e,t);return s?Promise.reject(s):Promise.resolve()}function E(e){const t=q.values().next().value;return t&&"function"==typeof t.runWithContext?t.runWithContext(e):e()}function T(e,t){let s;const[r,n,i]=function(e,t){const s=[],r=[],n=[],i=Math.max(t.matched.length,e.matched.length);for(let a=0;a<i;a++){const i=t.matched[a];i&&(e.matched.find(e=>st(e,i))?r.push(i):s.push(i));const o=e.matched[a];o&&(t.matched.find(e=>st(e,o))||n.push(o))}return[s,r,n]}(e,t);s=es(r.reverse(),"beforeRouteLeave",e,t);for(const o of r)o.leaveGuards.forEach(r=>{s.push(Xt(r,e,t))});const a=S.bind(null,e,t);return s.push(a),M(s).then(()=>{s=[];for(const r of l.list())s.push(Xt(r,e,t));return s.push(a),M(s)}).then(()=>{s=es(n,"beforeRouteUpdate",e,t);for(const r of n)r.updateGuards.forEach(r=>{s.push(Xt(r,e,t))});return s.push(a),M(s)}).then(()=>{s=[];for(const r of i)if(r.beforeEnter)if(Re(r.beforeEnter))for(const n of r.beforeEnter)s.push(Xt(n,e,t));else s.push(Xt(r.beforeEnter,e,t));return s.push(a),M(s)}).then(()=>(e.matched.forEach(e=>e.enterCallbacks={}),s=es(i,"beforeRouteEnter",e,t,E),s.push(a),M(s))).then(()=>{s=[];for(const r of c.list())s.push(Xt(r,e,t));return s.push(a),M(s)}).catch(e=>jt(e,8)?e:Promise.reject(e))}function j(e,t,s){u.list().forEach(r=>E(()=>r(e,t,s)))}function O(e,t,s,r,n){const i=b(e,t);if(i)return i;const a=t===at,l=Ce?history.state:{};s&&(r||a?o.replace(e.fullPath,Ae({scroll:a&&l&&l.scroll},n)):o.push(e.fullPath,n)),h.value=e,L(e,t,s,a),$()}let C;function P(){C||(C=o.listen((e,t,s)=>{if(!N.listening)return;const r=g(e),n=w(r);if(n)return void k(Ae(n,{replace:!0,force:!0}),r).catch(Ie);f=r;const i=h.value;var a,l;Ce&&(a=vt(i.fullPath,s.delta),l=pt(),gt.set(a,l)),T(r,i).catch(e=>jt(e,12)?e:jt(e,2)?(k(Ae(y(e.to),{force:!0}),r).then(e=>{jt(e,20)&&!s.delta&&s.type===ot.pop&&o.go(-1,!1)}).catch(Ie),Promise.reject()):(s.delta&&o.go(-s.delta,!1),R(e,r,i))).then(e=>{(e=e||O(r,i,!1))&&(s.delta&&!jt(e,8)?o.go(-s.delta,!1):s.type===ot.pop&&jt(e,20)&&o.go(-1,!1)),j(r,i,e)}).catch(Ie)}))}let A,x=Zt(),I=Zt();function R(e,t,s){$(e);const r=I.list();return r.length?r.forEach(r=>r(e,t,s)):console.error(e),Promise.reject(e)}function $(e){return A||(A=!e,P(),x.list().forEach(([t,s])=>e?s(e):t()),x.reset()),e}function L(t,s,r,n){const{scrollBehavior:i}=e;if(!Ce||!i)return Promise.resolve();const a=!r&&function(e){const t=gt.get(e);return gt.delete(e),t}(vt(t.fullPath,0))||(n||!r)&&history.state&&history.state.scroll||null;return d().then(()=>i(t,s,a)).then(e=>e&&mt(e)).catch(e=>R(e,t,s))}const U=e=>o.go(e);let D;const q=new Set,N={currentRoute:h,listening:!0,addRoute:function(e,s){let r,n;return wt(e)?(r=t.getRecordMatcher(e),n=s):n=e,t.addRoute(n,r)},removeRoute:function(e){const s=t.getRecordMatcher(e);s&&t.removeRoute(s)},clearRoutes:t.clearRoutes,hasRoute:function(e){return!!t.getRecordMatcher(e)},getRoutes:function(){return t.getRoutes().map(e=>e.record)},resolve:g,options:e,push:_,replace:function(e){return _(Ae(y(e),{replace:!0}))},go:U,back:()=>U(-1),forward:()=>U(1),beforeEach:l.add,beforeResolve:c.add,afterEach:u.add,onError:I.add,isReady:function(){return A&&h.value!==at?Promise.resolve():new Promise((e,t)=>{x.add([e,t])})},install(e){e.component("RouterLink",rs),e.component("RouterView",os),e.config.globalProperties.$router=this,Object.defineProperty(e.config.globalProperties,"$route",{enumerable:!0,get:()=>n(h)}),Ce&&!D&&h.value===at&&(D=!0,_(o.location).catch(e=>{}));const t={};for(const r in at)Object.defineProperty(t,r,{get:()=>h.value[r],enumerable:!0});e.provide(Ht,this),e.provide(Qt,i(t)),e.provide(Yt,h);const s=e.unmount;q.add(e),e.unmount=function(){q.delete(e),q.size<1&&(f=at,C&&C(),C=null,h.value=at,D=!1,A=!1),s()}}};function M(e){return e.reduce((e,t)=>e.then(()=>E(t)),Promise.resolve())}return N}({history:function(e){const t=_t(e=dt(e)),s=function(e,t,s,r){let n=[],i=[],a=null;const o=({state:i})=>{const o=yt(e,location),l=s.value,c=t.value;let u=0;if(i){if(s.value=o,t.value=i,a&&a===l)return void(a=null);u=c?i.position-c.position:0}else r(o);n.forEach(e=>{e(s.value,l,{delta:u,type:ot.pop,direction:u?u>0?ct.forward:ct.back:ct.unknown})})};function l(){const{history:e}=window;e.state&&e.replaceState(Ae({},e.state,{scroll:pt()}),"")}return window.addEventListener("popstate",o),window.addEventListener("beforeunload",l,{passive:!0}),{pauseListeners:function(){a=s.value},listen:function(e){n.push(e);const t=()=>{const t=n.indexOf(e);t>-1&&n.splice(t,1)};return i.push(t),t},destroy:function(){for(const e of i)e();i=[],window.removeEventListener("popstate",o),window.removeEventListener("beforeunload",l)}}}(e,t.state,t.location,t.replace),r=Ae({location:"",base:e,go:function(e,t=!0){t||s.pauseListeners(),history.go(e)},createHref:ft.bind(null,e)},t,s);return Object.defineProperty(r,"location",{enumerable:!0,get:()=>t.location.value}),Object.defineProperty(r,"state",{enumerable:!0,get:()=>t.state.value}),r}(),routes:Ou}),Pu=p("categories",()=>{const e=l([]),t=l(!1),s=l(null),r=l(!1);async function n(){if(!t.value){t.value=!0,s.value=null;try{const{data:t,error:s}=await Gi.from("categories").select("*").eq("is_active",!0).order("sort_order",{ascending:!0});if(s)throw s;e.value=t||[],r.value=!0}catch(n){console.error("获取分类失败:",n),s.value=n}finally{t.value=!1}}}return{categories:e,loading:t,error:s,initialized:r,fetchCategories:n,initialize:async function(){r.value||await n()},clearError:function(){s.value=null}}});const Au=je(ju),xu=Oe();Au.use(xu),Au.use(Cu),async function(){console.log("🚀 Initializing core stores and theme system...");const e=Yi(),t=Pu(),s=Qi();try{await Promise.all([e.initialize(),t.initialize(),s.initialize()]);const{applyTheme:r}=Sa();r(),console.log("✅ Core stores and theme system initialized successfully.")}catch(r){console.error("❌ Failed to initialize one or more stores:",r)}}().then(()=>{Au.mount("#app")});export{Ki as T,uo as _,Pu as a,Yi as b,ls as c,cs as d,aa as e,ds as f,Zi as g,Hi as h,Gi as s,Qi as u};
