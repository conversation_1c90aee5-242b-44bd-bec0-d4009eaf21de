name: Build Verification

on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build-verification:
    runs-on: ubuntu-latest
    name: Build Verification
    steps:
      - name: Manual checkout
        run: |
          echo "🔄 手动检出代码..."
          echo "当前工作目录: $(pwd)"

          # 使用 curl 下载源码
          echo "下载源码压缩包..."
          curl -L -o source.zip "https://github.com/jiayuwee/advanced-tools-navigation/archive/${{ github.sha }}.zip"

          # 解压源码
          echo "解压源码..."
          unzip -q source.zip

          # 移动文件到工作目录
          echo "移动文件到工作目录..."
          mv advanced-tools-navigation-*/* .
          mv advanced-tools-navigation-*/.[^.]* . 2>/dev/null || true

          # 清理
          rm -rf advanced-tools-navigation-* source.zip

          echo "当前目录内容:"
          ls -la

      - name: Setup Node.js
        run: |
          echo "🔧 设置 Node.js 环境..."
          node --version
          npm --version

      - name: Install dependencies
        run: |
          echo "📦 安装依赖..."
          echo "检查 package.json..."
          if [ -f "package.json" ]; then
            echo "package.json 存在，开始安装依赖..."
            echo "清理npm缓存..."
            npm cache clean --force
            echo "安装依赖（跳过可选依赖和平台特定依赖）..."
            npm install --prefer-offline --no-audit --no-optional --ignore-platform
            echo "✅ 依赖安装完成"
          else
            echo "❌ package.json 不存在"
            exit 1
          fi

      - name: Build project
        run: |
          echo "🔨 开始构建项目..."
          echo "检查环境变量..."
          echo "VITE_SUPABASE_URL: ${VITE_SUPABASE_URL:0:20}..."
          echo "VITE_SUPABASE_ANON_KEY: ${VITE_SUPABASE_ANON_KEY:+已设置}"
          npm run build
          echo "✅ 构建完成"
        env:
          VITE_SUPABASE_URL: ${{ secrets.VITE_SUPABASE_URL }}
          VITE_SUPABASE_ANON_KEY: ${{ secrets.VITE_SUPABASE_ANON_KEY }}
          NODE_ENV: production

      - name: Verify build output
        run: |
          echo "🔍 验证构建输出..."
          ls -la dist/
          if [ -f "dist/index.html" ]; then
            echo "✅ 构建成功 - index.html 存在"
            echo "📊 构建统计:"
            du -sh dist/
            echo "📁 主要文件:"
            ls -lh dist/assets/ | head -10
          else
            echo "❌ 构建失败 - index.html 不存在"
            exit 1
          fi

      - name: Deployment notification
        if: github.ref == 'refs/heads/main'
        run: |
          echo "🚀 代码已推送到 main 分支"
          echo "📦 Netlify 将自动检测并部署此次构建"
          echo "⏱️  部署通常需要 1-3 分钟"
          echo "🌐 网站地址: https://ramusi.cn"
          echo "✅ 构建验证通过，等待 Netlify 部署..."
