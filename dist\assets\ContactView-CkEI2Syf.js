import{d as e,b as s,r as t,m as a,a8 as l,q as r,y as o,u as n,ac as i,aJ as c,ad as d,ae as u,N as m,ab as x,a9 as b,af as p,x as g,A as f,B as v,D as h,v as y,t as w,aI as j,C as k,a1 as _,ay as q,a0 as V,aw as U,ax as C,G as z}from"./vendor-VsOxy-e0.js";const A={class:"min-h-screen bg-gray-50"},B={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},D={class:"grid grid-cols-1 lg:grid-cols-2 gap-12"},G={class:"space-y-6"},I={class:"flex items-start space-x-4"},J={class:"flex-shrink-0"},N={class:"w-12 h-12 bg-blue-100 rounded-lg flex items-center justify-center"},O={class:"flex items-start space-x-4"},P={class:"flex-shrink-0"},S={class:"w-12 h-12 bg-green-100 rounded-lg flex items-center justify-center"},T={class:"flex items-start space-x-4"},E={class:"flex-shrink-0"},F={class:"w-12 h-12 bg-purple-100 rounded-lg flex items-center justify-center"},H={class:"flex items-start space-x-4"},K={class:"flex-shrink-0"},L={class:"w-12 h-12 bg-orange-100 rounded-lg flex items-center justify-center"},M={class:"mt-12"},Q={class:"flex space-x-4"},R={href:"https://github.com/jiayuwee/advanced-tools-navigation",target:"_blank",class:"w-10 h-10 bg-gray-800 rounded-lg flex items-center justify-center text-white hover:bg-gray-700 transition-colors"},W={href:"#",class:"w-10 h-10 bg-green-500 rounded-lg flex items-center justify-center text-white hover:bg-green-600 transition-colors"},X={class:"mt-8 bg-blue-50 rounded-lg p-6"},Y={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},Z={class:"bg-white rounded-lg shadow-md p-8"},$=["disabled"],ee={class:"mt-8 bg-gray-100 rounded-lg p-6"},se={class:"space-y-3"},te=e({__name:"ContactView",setup(e){const te=s({name:"",email:"",phone:"",subject:"",message:""}),ae=t(!1),le=async()=>{if(te.name&&te.email&&te.subject&&te.message){ae.value=!0;try{console.log("提交联系表单:",te),await new Promise(e=>setTimeout(e,2e3)),alert("消息发送成功！我们会尽快回复您。"),Object.assign(te,{name:"",email:"",phone:"",subject:"",message:""})}catch(e){console.error("发送失败:",e),alert("发送失败，请稍后重试")}finally{ae.value=!1}}else alert("请填写必填字段")};return document.title="联系我们 - 工具导航站",(e,s)=>{const t=V("router-link");return z(),a("div",A,[s[25]||(s[25]=l('<div class="bg-white shadow-sm"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="text-center"><h1 class="text-3xl font-bold text-gray-900">联系我们</h1><p class="mt-4 text-lg text-gray-600"> 多种方式联系我们，我们随时为您提供帮助 </p></div></div></div>',1)),r("div",B,[r("div",D,[r("div",null,[s[13]||(s[13]=r("h2",{class:"text-2xl font-bold text-gray-900 mb-8"},"联系方式",-1)),r("div",G,[r("div",I,[r("div",J,[r("div",N,[o(n(i),{class:"w-6 h-6 text-blue-600"})])]),s[5]||(s[5]=r("div",null,[r("h3",{class:"text-lg font-semibold text-gray-900"},"客服邮箱"),r("p",{class:"text-gray-600 mt-1"},"一般问题和技术支持"),r("a",{href:"mailto:<EMAIL>",class:"text-blue-600 hover:text-blue-800 font-medium"}," <EMAIL> ")],-1))]),r("div",O,[r("div",P,[r("div",S,[o(n(c),{class:"w-6 h-6 text-green-600"})])]),s[6]||(s[6]=r("div",null,[r("h3",{class:"text-lg font-semibold text-gray-900"},"商务合作"),r("p",{class:"text-gray-600 mt-1"},"合作洽谈和商务咨询"),r("a",{href:"mailto:<EMAIL>",class:"text-blue-600 hover:text-blue-800 font-medium"}," <EMAIL> ")],-1))]),r("div",T,[r("div",E,[r("div",F,[o(n(d),{class:"w-6 h-6 text-purple-600"})])]),s[7]||(s[7]=r("div",null,[r("h3",{class:"text-lg font-semibold text-gray-900"},"客服电话"),r("p",{class:"text-gray-600 mt-1"},"工作日 9:00-18:00"),r("a",{href:"tel:+8613800138000",class:"text-blue-600 hover:text-blue-800 font-medium"}," +86 138-0013-8000 ")],-1))]),r("div",H,[r("div",K,[r("div",L,[o(n(u),{class:"w-6 h-6 text-orange-600"})])]),s[8]||(s[8]=r("div",null,[r("h3",{class:"text-lg font-semibold text-gray-900"},"公司地址"),r("p",{class:"text-gray-600 mt-1"},[m(" 中国 北京市 海淀区"),r("br"),m(" 中关村软件园 2号楼 ")])],-1))])]),r("div",M,[s[9]||(s[9]=r("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"关注我们",-1)),r("div",Q,[r("a",R,[o(n(x),{class:"w-5 h-5"})]),r("a",W,[o(n(b),{class:"w-5 h-5"})])]),s[10]||(s[10]=r("p",{class:"text-sm text-gray-600 mt-2"},"微信公众号：ramusi_tools",-1))]),r("div",X,[r("h3",Y,[o(n(p),{class:"w-5 h-5 mr-2 text-blue-600"}),s[11]||(s[11]=m(" 工作时间 "))]),s[12]||(s[12]=l('<div class="space-y-2 text-sm text-gray-700"><div class="flex justify-between"><span>周一至周五</span><span>9:00 - 18:00</span></div><div class="flex justify-between"><span>周六</span><span>10:00 - 16:00</span></div><div class="flex justify-between"><span>周日</span><span>休息</span></div></div><p class="text-xs text-gray-600 mt-3"> * 紧急问题请发送邮件，我们会尽快回复 </p>',2))])]),r("div",null,[s[24]||(s[24]=r("h2",{class:"text-2xl font-bold text-gray-900 mb-8"},"快速联系",-1)),r("div",Z,[r("form",{onSubmit:g(le,["prevent"]),class:"space-y-6"},[r("div",null,[s[14]||(s[14]=r("label",{for:"name",class:"block text-sm font-medium text-gray-700 mb-2"},[m(" 姓名 "),r("span",{class:"text-red-500"},"*")],-1)),f(r("input",{id:"name","onUpdate:modelValue":s[0]||(s[0]=e=>te.name=e),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的姓名"},null,512),[[v,te.name]])]),r("div",null,[s[15]||(s[15]=r("label",{for:"email",class:"block text-sm font-medium text-gray-700 mb-2"},[m(" 邮箱 "),r("span",{class:"text-red-500"},"*")],-1)),f(r("input",{id:"email","onUpdate:modelValue":s[1]||(s[1]=e=>te.email=e),type:"email",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的邮箱地址"},null,512),[[v,te.email]])]),r("div",null,[s[16]||(s[16]=r("label",{for:"phone",class:"block text-sm font-medium text-gray-700 mb-2"}," 电话 ",-1)),f(r("input",{id:"phone","onUpdate:modelValue":s[2]||(s[2]=e=>te.phone=e),type:"tel",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入您的电话号码"},null,512),[[v,te.phone]])]),r("div",null,[s[18]||(s[18]=r("label",{for:"subject",class:"block text-sm font-medium text-gray-700 mb-2"},[m(" 主题 "),r("span",{class:"text-red-500"},"*")],-1)),f(r("select",{id:"subject","onUpdate:modelValue":s[3]||(s[3]=e=>te.subject=e),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},s[17]||(s[17]=[l('<option value="">请选择主题</option><option value="general">一般咨询</option><option value="technical">技术支持</option><option value="business">商务合作</option><option value="feedback">意见反馈</option><option value="other">其他</option>',6)]),512),[[h,te.subject]])]),r("div",null,[s[19]||(s[19]=r("label",{for:"message",class:"block text-sm font-medium text-gray-700 mb-2"},[m(" 消息 "),r("span",{class:"text-red-500"},"*")],-1)),f(r("textarea",{id:"message","onUpdate:modelValue":s[4]||(s[4]=e=>te.message=e),rows:"5",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"请详细描述您的问题或需求..."},null,512),[[v,te.message]])]),r("button",{type:"submit",disabled:ae.value,class:"w-full px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center justify-center"},[ae.value?(z(),y(n(j),{key:0,class:"w-4 h-4 mr-2 animate-spin"})):w("",!0),m(" "+k(ae.value?"发送中...":"发送消息"),1)],8,$)],32)]),r("div",ee,[s[23]||(s[23]=r("h3",{class:"text-lg font-semibold text-gray-900 mb-4"},"其他方式",-1)),r("div",se,[o(t,{to:"/feedback",class:"flex items-center text-blue-600 hover:text-blue-800 transition-colors"},{default:_(()=>[o(n(q),{class:"w-4 h-4 mr-2"}),s[20]||(s[20]=m(" 提交意见反馈 "))]),_:1,__:[20]}),o(t,{to:"/faq",class:"flex items-center text-blue-600 hover:text-blue-800 transition-colors"},{default:_(()=>[o(n(U),{class:"w-4 h-4 mr-2"}),s[21]||(s[21]=m(" 查看常见问题 "))]),_:1,__:[21]}),o(t,{to:"/user-guide",class:"flex items-center text-blue-600 hover:text-blue-800 transition-colors"},{default:_(()=>[o(n(C),{class:"w-4 h-4 mr-2"}),s[22]||(s[22]=m(" 阅读使用指南 "))]),_:1,__:[22]})])])])])])])}}});export{te as default};
