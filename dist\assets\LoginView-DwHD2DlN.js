import{d as a,r as e,m as l,q as s,t as o,A as r,B as u,aV as i,v as t,u as n,aQ as d,aW as c,y as p,N as v,E as m,a1 as b,a0 as f,C as g,x as y,G as w}from"./vendor-VsOxy-e0.js";import{c as k,_ as h}from"./index-DdsLZGoA.js";const _={class:"login-view"},x={class:"form-group"},V=["disabled"],q={class:"form-group"},U={class:"password-input"},j=["type","disabled"],C={class:"form-options"},E={class:"checkbox-label"},A=["disabled"],B={key:0,class:"loading-spinner"},G={key:0,class:"error-message"},I={class:"login-footer"},L=h(a({__name:"LoginView",setup(a){const h=k(),L=e(!1),N=e(null),P=e(!1),Q=e({email:"",password:"",remember:!1}),S=async()=>{try{L.value=!0,N.value=null,await new Promise(a=>setTimeout(a,1e3)),console.log("登录成功:",Q.value.email),h.push("/")}catch(a){N.value=a instanceof Error?a.message:"登录失败，请重试"}finally{L.value=!1}};return(a,e)=>{const k=f("router-link");return w(),l("div",_,[e[11]||(e[11]=s("div",{class:"login-header"},[s("h1",null,"登录"),s("p",null,"欢迎回来，请登录您的账户")],-1)),s("form",{class:"login-form",onSubmit:y(S,["prevent"])},[s("div",x,[e[4]||(e[4]=s("label",{for:"email"},"邮箱地址",-1)),r(s("input",{id:"email","onUpdate:modelValue":e[0]||(e[0]=a=>Q.value.email=a),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:L.value},null,8,V),[[u,Q.value.email]])]),s("div",q,[e[5]||(e[5]=s("label",{for:"password"},"密码",-1)),s("div",U,[r(s("input",{id:"password","onUpdate:modelValue":e[1]||(e[1]=a=>Q.value.password=a),type:P.value?"text":"password",required:"",placeholder:"请输入您的密码",disabled:L.value},null,8,j),[[i,Q.value.password]]),s("button",{type:"button",class:"password-toggle",onClick:e[2]||(e[2]=a=>P.value=!P.value)},[P.value?(w(),t(n(c),{key:1,class:"icon"})):(w(),t(n(d),{key:0,class:"icon"}))])])]),s("div",C,[s("label",E,[r(s("input",{"onUpdate:modelValue":e[3]||(e[3]=a=>Q.value.remember=a),type:"checkbox"},null,512),[[m,Q.value.remember]]),e[6]||(e[6]=s("span",{class:"checkmark"},null,-1)),e[7]||(e[7]=v(" 记住我 "))]),p(k,{to:"/auth/forgot-password",class:"forgot-link"},{default:b(()=>e[8]||(e[8]=[v(" 忘记密码？ ")])),_:1,__:[8]})]),s("button",{type:"submit",class:"login-btn",disabled:L.value},[L.value?(w(),l("div",B)):o("",!0),s("span",null,g(L.value?"登录中...":"登录"),1)],8,A),N.value?(w(),l("div",G,g(N.value),1)):o("",!0)],32),s("div",I,[s("p",null,[e[10]||(e[10]=v(" 还没有账户？ ")),p(k,{to:"/auth/register",class:"register-link"},{default:b(()=>e[9]||(e[9]=[v("立即注册")])),_:1,__:[9]})])])])}}}),[["__scopeId","data-v-61ca95f0"]]);export{L as default};
