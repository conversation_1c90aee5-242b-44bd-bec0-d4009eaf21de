import{d as s,c as a,_ as c}from"./index-DdsLZGoA.js";import{d as t,r as l,o as e,m as n,q as o,t as i,y as p,u as d,U as u,C as r,b0 as v,Q as m,a1 as _,N as f,a0 as h,b5 as b,ac as y,ad as x,G as C}from"./vendor-VsOxy-e0.js";const k={class:"payment-cancel-view"},w={class:"cancel-container"},g={class:"cancel-content"},j={class:"cancel-icon"},q={key:0,class:"order-info"},N={class:"info-item"},z={class:"value"},D={class:"info-item"},G={class:"value"},I={class:"next-steps"},L={class:"steps-list"},P={class:"step-item"},Q={class:"step-item"},S={class:"step-content"},U={class:"step-item"},V={class:"action-buttons"},$={class:"support-info"},A={class:"contact-methods"},B={href:"mailto:<EMAIL>",class:"contact-item"},E={href:"tel:************",class:"contact-item"},F=c(t({__name:"PaymentCancelView",setup(c){const t=s(),F=a(),H=l(""),J=l(""),K=()=>{H.value?F.push(`/payment?order=${H.value}`):F.push("/payment")};return e(()=>{(()=>{const s=t.query.order;s&&(H.value=s),J.value=(new Date).toLocaleString("zh-CN")})()}),(s,a)=>{const c=h("router-link");return C(),n("div",k,[o("div",w,[o("div",g,[o("div",j,[p(d(u),{class:"icon"})]),a[14]||(a[14]=o("h1",{class:"cancel-title"},"支付已取消",-1)),a[15]||(a[15]=o("p",{class:"cancel-message"},"您的支付已被取消，订单未完成",-1)),H.value?(C(),n("div",q,[o("div",N,[a[0]||(a[0]=o("span",{class:"label"},"订单号:",-1)),o("span",z,r(H.value),1)]),o("div",D,[a[1]||(a[1]=o("span",{class:"label"},"取消时间:",-1)),o("span",G,r(J.value),1)])])):i("",!0),o("div",I,[a[8]||(a[8]=o("h3",null,"接下来您可以：",-1)),o("div",L,[o("div",P,[p(d(v),{class:"step-icon"}),o("div",{class:"step-content"},[a[2]||(a[2]=o("h4",null,"重新支付",-1)),a[3]||(a[3]=o("p",null,"返回支付页面完成订单支付",-1)),o("button",{class:"step-action",onClick:K}," 重新支付 ")])]),o("div",Q,[p(d(m),{class:"step-icon"}),o("div",S,[a[5]||(a[5]=o("h4",null,"继续购物",-1)),a[6]||(a[6]=o("p",null,"浏览更多优质产品",-1)),p(c,{to:"/products",class:"step-action"},{default:_(()=>a[4]||(a[4]=[f(" 继续购物 ")])),_:1,__:[4]})])]),o("div",U,[p(d(b),{class:"step-icon"}),a[7]||(a[7]=o("div",{class:"step-content"},[o("h4",null,"联系客服"),o("p",null,"如有疑问，请联系我们的客服团队")],-1))])])]),o("div",V,[p(c,{to:"/",class:"btn btn-secondary"},{default:_(()=>a[9]||(a[9]=[f(" 返回首页 ")])),_:1,__:[9]}),p(c,{to:"/products",class:"btn btn-primary"},{default:_(()=>a[10]||(a[10]=[f(" 继续购物 ")])),_:1,__:[10]})]),o("div",$,[a[13]||(a[13]=o("p",null,"如需帮助，请联系我们的客服团队",-1)),o("div",A,[o("a",B,[p(d(y),{class:"contact-icon"}),a[11]||(a[11]=f(" <EMAIL> "))]),o("a",E,[p(d(x),{class:"contact-icon"}),a[12]||(a[12]=f(" ************ "))])])])])])])}}}),[["__scopeId","data-v-2c046c66"]]);export{F as default};
