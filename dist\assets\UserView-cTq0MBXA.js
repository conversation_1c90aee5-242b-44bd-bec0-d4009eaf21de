import{d as s,m as a,q as e,y as r,a0 as o,G as t}from"./vendor-VsOxy-e0.js";import{_ as n}from"./index-DdsLZGoA.js";const i={class:"user-view"},d={class:"container"},c=n(s({__name:"UserView",setup:s=>(s,n)=>{const c=o("router-view");return t(),a("div",i,[e("div",d,[n[0]||(n[0]=e("h1",null,"个人中心",-1)),n[1]||(n[1]=e("p",null,"用户功能正在开发中...",-1)),r(c)])])}}),[["__scopeId","data-v-9b4e7b4c"]]);export{c as default};
