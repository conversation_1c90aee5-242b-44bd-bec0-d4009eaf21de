const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./orderService-Dms8cMn5.js","./index-DdsLZGoA.js","./vendor-VsOxy-e0.js","./index-BukxQyzs.css"])))=>i.map(i=>d[i]);
import{d as s,f as a,_ as e}from"./index-DdsLZGoA.js";import{d as t,r as c,o as l,m as o,q as n,y as r,u as i,W as u,C as d,aU as p,ac as v,aY as m,a1 as _,N as f,a0 as h,ad as y,G as b}from"./vendor-VsOxy-e0.js";const w={class:"payment-success-view"},S={class:"success-container"},O={class:"success-content"},g={class:"success-icon"},x={class:"order-info"},A={class:"info-item"},C={class:"value"},E={class:"info-item"},j={class:"value amount"},k={class:"info-item"},q={class:"value"},D={class:"info-item"},I={class:"value"},P={class:"next-steps"},L={class:"steps-list"},R={class:"step-item"},T={class:"step-item"},U={class:"step-item"},V={class:"step-content"},z={class:"action-buttons"},N={class:"support-info"},B={class:"contact-methods"},F={href:"mailto:<EMAIL>",class:"contact-item"},G={href:"tel:************",class:"contact-item"},W=e(t({__name:"PaymentSuccessView",setup(e){const t=s(),W=c("ORD-20241224-001"),Y=c(299),$=c(""),H=c("支付宝"),J=async()=>{var s;try{console.log("开始下载产品...");const e=t.query.order;if(!e)return void alert("订单信息不存在");const{OrderService:c}=await a(async()=>{const{OrderService:s}=await import("./orderService-Dms8cMn5.js");return{OrderService:s}},__vite__mapDeps([0,1,2,3]),import.meta.url),{useAuthStore:l}=await a(async()=>{const{useAuthStore:s}=await import("./index-DdsLZGoA.js").then(s=>s.g);return{useAuthStore:s}},__vite__mapDeps([1,2,3]),import.meta.url),o=l();if(!o.user)return void alert("请先登录");const n=await c.getOrderById(e,o.user.id);if(!n||"paid"!==n.status)return void alert("订单不存在或未支付");for(const a of n.items)if(null==(s=a.product)?void 0:s.downloadUrl){const s=document.createElement("a");s.href=a.product.downloadUrl,s.download=`${a.product.name}.zip`,s.target="_blank",document.body.appendChild(s),s.click(),document.body.removeChild(s),await new Promise(s=>setTimeout(s,500))}}catch(e){console.error("下载失败:",e),alert("下载失败，请稍后重试")}};return l(()=>{(()=>{const s=t.query.order,a=t.query.amount;s&&(W.value=s),a&&(Y.value=parseFloat(a)),$.value=(new Date).toLocaleString("zh-CN")})()}),(s,a)=>{const e=h("router-link");return b(),o("div",w,[n("div",S,[n("div",O,[n("div",g,[r(i(u),{class:"icon"})]),a[16]||(a[16]=n("h1",{class:"success-title"},"支付成功！",-1)),a[17]||(a[17]=n("p",{class:"success-message"},"您的订单已成功支付，感谢您的购买",-1)),n("div",x,[n("div",A,[a[0]||(a[0]=n("span",{class:"label"},"订单号:",-1)),n("span",C,d(W.value),1)]),n("div",E,[a[1]||(a[1]=n("span",{class:"label"},"支付金额:",-1)),n("span",j,"¥"+d(Y.value),1)]),n("div",k,[a[2]||(a[2]=n("span",{class:"label"},"支付时间:",-1)),n("span",q,d($.value),1)]),n("div",D,[a[3]||(a[3]=n("span",{class:"label"},"支付方式:",-1)),n("span",I,d(H.value),1)])]),n("div",P,[a[10]||(a[10]=n("h3",null,"接下来您可以：",-1)),n("div",L,[n("div",R,[r(i(p),{class:"step-icon"}),n("div",{class:"step-content"},[a[4]||(a[4]=n("h4",null,"下载产品",-1)),a[5]||(a[5]=n("p",null,"立即下载您购买的数字产品",-1)),n("button",{class:"step-action",onClick:J}," 立即下载 ")])]),n("div",T,[r(i(v),{class:"step-icon"}),a[6]||(a[6]=n("div",{class:"step-content"},[n("h4",null,"查看邮件"),n("p",null,"我们已向您的邮箱发送了订单确认邮件")],-1))]),n("div",U,[r(i(m),{class:"step-icon"}),n("div",V,[a[8]||(a[8]=n("h4",null,"查看订单",-1)),a[9]||(a[9]=n("p",null,"在个人中心查看完整的订单详情",-1)),r(e,{to:"/user/orders",class:"step-action"},{default:_(()=>a[7]||(a[7]=[f(" 查看订单 ")])),_:1,__:[7]})])])])]),n("div",z,[r(e,{to:"/",class:"btn btn-secondary"},{default:_(()=>a[11]||(a[11]=[f(" 返回首页 ")])),_:1,__:[11]}),r(e,{to:"/products",class:"btn btn-primary"},{default:_(()=>a[12]||(a[12]=[f(" 继续购物 ")])),_:1,__:[12]})]),n("div",N,[a[15]||(a[15]=n("p",null,"如有任何问题，请联系我们的客服团队",-1)),n("div",B,[n("a",F,[r(i(v),{class:"contact-icon"}),a[13]||(a[13]=f(" <EMAIL> "))]),n("a",G,[r(i(y),{class:"contact-icon"}),a[14]||(a[14]=f(" ************ "))])])])])])])}}}),[["__scopeId","data-v-743994ad"]]);export{W as default};
