# Supabase 项目配置文件

[api]
enabled = true
port = 54321
schemas = ["public", "graphql_public"]
extra_search_path = ["public", "extensions"]
max_rows = 1000

[auth]
enabled = true
site_url = "http://localhost:3000"
additional_redirect_urls = ["https://ramusi.cn", "https://spiffy-torrone-5454e1.netlify.app"]
jwt_expiry = 3600
enable_signup = true

[auth.email]
enable_signup = true
double_confirm_changes = true
enable_confirmations = true

[auth.sms]
enable_signup = false
enable_confirmations = false

[auth.external.apple]
enabled = false

[auth.external.azure]
enabled = false

[auth.external.bitbucket]
enabled = false

[auth.external.discord]
enabled = false

[auth.external.facebook]
enabled = false

[auth.external.github]
enabled = true
client_id = "env(GITHUB_CLIENT_ID)"
secret = "env(GITHUB_SECRET)"

[auth.external.gitlab]
enabled = false

[auth.external.google]
enabled = true
client_id = "env(GOOGLE_CLIENT_ID)"
secret = "env(GOOGLE_SECRET)"

[auth.external.keycloak]
enabled = false

[auth.external.linkedin]
enabled = false

[auth.external.notion]
enabled = false

[auth.external.twitch]
enabled = false

[auth.external.twitter]
enabled = false

[auth.external.slack]
enabled = false

[auth.external.spotify]
enabled = false

[auth.external.workos]
enabled = false

[auth.external.zoom]
enabled = false

[db]
port = 54322
shadow_port = 54320
major_version = 15

[db.pooler]
enabled = false
port = 54329
pool_mode = "transaction"
default_pool_size = 20
max_client_conn = 100

[realtime]
enabled = true
ip_version = "IPv4"

[studio]
enabled = true
api_url = "http://localhost:54321"

[inbucket]
enabled = true
smtp_port = 54325
pop3_port = 54326

[storage]
enabled = true
file_size_limit = "50MiB"

[storage.image_transformation]
enabled = true

[analytics]
enabled = false
port = 54327
vector_port = 54328
gcp_project_id = ""
gcp_project_number = ""
gcp_jwt_path = "supabase/gcp.json"

[functions]

[functions.verify_jwt]
enabled = false

[edge_runtime]
enabled = true
