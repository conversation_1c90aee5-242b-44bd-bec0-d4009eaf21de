{"compilerOptions": {"target": "ESNext", "useDefineForClassFields": true, "module": "ESNext", "lib": ["ESNext", "DOM"], "skipLibCheck": true, "esModuleInterop": true, "moduleResolution": "Node", "strict": false, "jsx": "preserve", "resolveJsonModule": true, "isolatedModules": true, "noUnusedLocals": false, "noUnusedParameters": false, "noImplicitReturns": false, "noFallthroughCasesInSwitch": false, "baseUrl": ".", "paths": {"@/*": ["./src/*"], "@config/*": ["./config/*"], "@scripts/*": ["./scripts/*"], "@docs/*": ["./docs/*"]}}, "include": ["env.d.ts", "src/**/*", "src/**/*.vue", "vite.config.*", "supabase/**/*.ts"], "exclude": ["src/**/__tests__/*"]}