import{r as e,c as a,d as s,w as l,m as t,H as r,q as c,y as o,u as i,X as n,t as u,A as v,D as d,F as p,z as g,C as h,B as m,a3 as y,N as f,a6 as k,E as b,v as C,aL as w,aM as _,af as U,at as O,aN as x,a2 as S,G as F,o as L,aO as E,as as A,aq as B,aP as V,x as q,aQ as H,av as T,P as D,O as Q}from"./vendor-VsOxy-e0.js";import{u as R,_ as j,c as z,d as I}from"./index-DdsLZGoA.js";function $(){const s=R(),l=e(""),t=e({category:"",tags:[],rating:0,isFeatured:!1,hasUrl:!1,sortBy:"name",sortOrder:"asc"}),r=e([]),c=e(!1),o=a(()=>{if(!s.tools.length)return[];c.value=!0;try{let e=((e,a)=>{if(!e.trim())return a.map(e=>({item:e,score:1,matches:[]}));const s=e.toLowerCase().split(/\s+/).filter(e=>e.length>0),l=[];return a.forEach(e=>{let a=0;const t=[],r=[{field:"name",weight:10},{field:"description",weight:5},{field:"tags",weight:3},{field:"categories.name",weight:2}];s.forEach(s=>{var l;r.forEach(({field:l,weight:r})=>{const c=u(e,l);c&&v(c,s)&&(a+=r,t.includes(l)||t.push(l))}),d((null==(l=e.name)?void 0:l.toLowerCase())||"",s)&&(a+=2)}),e.is_featured&&(a+=1),e.average_rating&&e.average_rating>4&&(a+=1),a>0&&l.push({item:e,score:a,matches:t})}),l.sort((e,a)=>a.score-e.score)})(l.value,s.tools);return e=(e=>e.filter(({item:e})=>{if(t.value.category&&e.category_id!==t.value.category)return!1;if(t.value.tags.length>0){const a=e.tags||[];if(!t.value.tags.some(e=>a.some(a=>a.toLowerCase().includes(e.toLowerCase()))))return!1}return!(t.value.rating>0&&(e.average_rating||0)<t.value.rating)&&!(t.value.isFeatured&&!e.is_featured||!(!t.value.hasUrl||e.url&&e.url.trim()))}))(e),e=(e=>e.sort((e,a)=>{const{sortBy:s,sortOrder:l}=t.value;let r=0;switch(s){case"name":r=(e.item.name||"").localeCompare(a.item.name||"");break;case"rating":r=(e.item.average_rating||0)-(a.item.average_rating||0);break;case"clicks":r=(e.item.click_count||0)-(a.item.click_count||0);break;case"created_at":r=new Date(e.item.created_at||0).getTime()-new Date(a.item.created_at||0).getTime();break;default:r=a.score-e.score}return"desc"===l?-r:r}))(e),e}finally{c.value=!1}}),i=a(()=>{if(!l.value.trim())return[];const e=l.value.toLowerCase(),a=new Set;return s.tools.forEach(s=>{var l;(null==(l=s.name)?void 0:l.toLowerCase().includes(e))&&a.add(s.name),s.tags&&s.tags.forEach(s=>{s.toLowerCase().includes(e)&&a.add(s)})}),Array.from(a).slice(0,5)}),n=a(()=>{const e=r.value.reduce((e,a)=>(e[a]=(e[a]||0)+1,e),{});return Object.entries(e).sort(([,e],[,a])=>a-e).slice(0,10).map(([e])=>e)}),u=(e,a)=>a.split(".").reduce((e,a)=>null==e?void 0:e[a],e),v=(e,a)=>Array.isArray(e)?e.some(e=>"string"==typeof e&&e.toLowerCase().includes(a)):"string"==typeof e&&e.toLowerCase().includes(a),d=(e,a)=>{const s=a.length,l=e.length;if(s>l)return!1;if(s===l)return a===e;let t=0;for(let r=0;r<l&&t<s;r++)e[r]===a[t]&&t++;return t===s};return{searchQuery:l,filters:t,searchResults:o,searchSuggestions:i,popularSearches:n,searchHistory:r,isSearching:c,search:e=>{l.value=e,e.trim()&&!r.value.includes(e)&&(r.value.unshift(e),r.value.length>50&&(r.value=r.value.slice(0,50)))},clearSearch:()=>{l.value=""},resetFilters:()=>{t.value={category:"",tags:[],rating:0,isFeatured:!1,hasUrl:!1,sortBy:"name",sortOrder:"asc"}}}}const P={class:"panel-header"},M={class:"panel-content"},N={class:"filter-group"},W=["value"],G={class:"filter-group"},K={class:"tags-container"},J={class:"popular-tags"},X=["onClick"],Y={key:0,class:"selected-tags"},Z=["onClick"],ee={class:"filter-group"},ae={class:"rating-filter"},se={class:"rating-stars"},le={class:"rating-text"},te={class:"filter-group"},re={class:"checkbox-group"},ce={class:"checkbox-item"},oe={class:"checkbox-item"},ie={class:"filter-group"},ne={class:"sort-options"},ue=["title"],ve={key:0,class:"filter-group"},de={class:"search-history"},pe=["onClick"],ge={key:1,class:"filter-group"},he={class:"popular-searches"},me=["onClick"],ye={class:"panel-footer"},fe=j(s({__name:"AdvancedSearchPanel",props:{isOpen:{type:Boolean},filters:{},searchHistory:{},popularSearches:{}},emits:["close","update:filters","search"],setup(s,{emit:L}){const E=s,A=L,B=R(),V=e(""),q=e({...E.filters});l(()=>E.filters,e=>{q.value={...e}},{deep:!0});const H=a(()=>B.categories),T=a(()=>{const e=new Map;return B.tools.forEach(a=>{a.tags&&a.tags.forEach(a=>{e.set(a,(e.get(a)||0)+1)})}),Array.from(e.entries()).sort(([,e],[,a])=>a-e).slice(0,10).map(([e])=>e)}),D=()=>{const e=V.value.trim();e&&!q.value.tags.includes(e)&&(q.value.tags.push(e),V.value="")},Q=e=>{q.value.rating=e},j=()=>{q.value.sortOrder="asc"===q.value.sortOrder?"desc":"asc"},z=()=>{q.value={category:"",tags:[],rating:0,isFeatured:!1,hasUrl:!1,sortBy:"name",sortOrder:"asc"},I()},I=()=>{A("update:filters",{...q.value})};return l(q,()=>{I()},{deep:!0}),(e,a)=>{return F(),t("div",{class:r(["advanced-search-panel",{"is-open":e.isOpen}])},[c("div",P,[a[7]||(a[7]=c("h3",null,"高级搜索",-1)),c("button",{onClick:a[0]||(a[0]=a=>e.$emit("close")),class:"close-button"},[o(i(n),{class:"icon"})])]),c("div",M,[c("div",N,[a[9]||(a[9]=c("label",{class:"filter-label"},"分类",-1)),v(c("select",{"onUpdate:modelValue":a[1]||(a[1]=e=>q.value.category=e),class:"filter-select"},[a[8]||(a[8]=c("option",{value:""},"所有分类",-1)),(F(!0),t(p,null,g(H.value,e=>(F(),t("option",{key:e.id,value:e.id},h(e.name),9,W))),128))],512),[[d,q.value.category]])]),c("div",G,[a[12]||(a[12]=c("label",{class:"filter-label"},"标签",-1)),c("div",K,[c("div",J,[a[10]||(a[10]=c("span",{class:"tags-subtitle"},"热门标签:",-1)),(F(!0),t(p,null,g(T.value,e=>(F(),t("button",{key:e,onClick:a=>(e=>{const a=q.value.tags.indexOf(e);a>-1?q.value.tags.splice(a,1):q.value.tags.push(e)})(e),class:r(["tag-button",{active:q.value.tags.includes(e)}])},h(e),11,X))),128))]),v(c("input",{"onUpdate:modelValue":a[2]||(a[2]=e=>V.value=e),onKeydown:y(D,["enter"]),placeholder:"输入自定义标签...",class:"tag-input"},null,544),[[m,V.value]]),q.value.tags.length>0?(F(),t("div",Y,[a[11]||(a[11]=c("span",{class:"tags-subtitle"},"已选标签:",-1)),(F(!0),t(p,null,g(q.value.tags,e=>(F(),t("span",{key:e,class:"selected-tag"},[f(h(e)+" ",1),c("button",{onClick:a=>(e=>{const a=q.value.tags.indexOf(e);a>-1&&q.value.tags.splice(a,1)})(e),class:"tag-remove"},"×",8,Z)]))),128))])):u("",!0)])]),c("div",ee,[a[13]||(a[13]=c("label",{class:"filter-label"},"最低评分",-1)),c("div",ae,[c("div",se,[(F(),t(p,null,g(5,e=>o(i(k),{key:e,class:r([{filled:e<=q.value.rating},"star"]),onClick:a=>Q(e)},null,8,["class","onClick"])),64))]),c("span",le,h((s=q.value.rating,["不限","1星以上","2星以上","3星以上","4星以上","5星"][s]||"不限")),1),q.value.rating>0?(F(),t("button",{key:0,onClick:a[3]||(a[3]=e=>Q(0)),class:"clear-rating"}," 清除 ")):u("",!0)])]),c("div",te,[a[16]||(a[16]=c("label",{class:"filter-label"},"特殊筛选",-1)),c("div",re,[c("label",ce,[v(c("input",{type:"checkbox","onUpdate:modelValue":a[4]||(a[4]=e=>q.value.isFeatured=e)},null,512),[[b,q.value.isFeatured]]),a[14]||(a[14]=c("span",{class:"checkbox-label"},"仅显示特色工具",-1))]),c("label",oe,[v(c("input",{type:"checkbox","onUpdate:modelValue":a[5]||(a[5]=e=>q.value.hasUrl=e)},null,512),[[b,q.value.hasUrl]]),a[15]||(a[15]=c("span",{class:"checkbox-label"},"仅显示可访问工具",-1))])])]),c("div",ie,[a[18]||(a[18]=c("label",{class:"filter-label"},"排序方式",-1)),c("div",ne,[v(c("select",{"onUpdate:modelValue":a[6]||(a[6]=e=>q.value.sortBy=e),class:"sort-select"},a[17]||(a[17]=[c("option",{value:"name"},"名称",-1),c("option",{value:"rating"},"评分",-1),c("option",{value:"clicks"},"使用次数",-1),c("option",{value:"created_at"},"创建时间",-1)]),512),[[d,q.value.sortBy]]),c("button",{onClick:j,class:"sort-order-button",title:"asc"===q.value.sortOrder?"升序":"降序"},["asc"===q.value.sortOrder?(F(),C(i(w),{key:0,class:"icon"})):(F(),C(i(_),{key:1,class:"icon"}))],8,ue)])]),e.searchHistory.length>0?(F(),t("div",ve,[a[19]||(a[19]=c("label",{class:"filter-label"},"搜索历史",-1)),c("div",de,[(F(!0),t(p,null,g(e.searchHistory.slice(0,5),(a,s)=>(F(),t("button",{key:s,onClick:s=>e.$emit("search",a),class:"history-item"},[o(i(U),{class:"icon"}),f(" "+h(a),1)],8,pe))),128))])])):u("",!0),e.popularSearches.length>0?(F(),t("div",ge,[a[20]||(a[20]=c("label",{class:"filter-label"},"热门搜索",-1)),c("div",he,[(F(!0),t(p,null,g(e.popularSearches.slice(0,5),a=>(F(),t("button",{key:a,onClick:s=>e.$emit("search",a),class:"popular-item"},[o(i(O),{class:"icon"}),f(" "+h(a),1)],8,me))),128))])])):u("",!0)]),c("div",ye,[c("button",{onClick:z,class:"reset-button"},[o(i(x),{class:"icon"}),a[21]||(a[21]=f(" 重置筛选 "))]),c("button",{onClick:I,class:"apply-button"},[o(i(S),{class:"icon"}),a[22]||(a[22]=f(" 应用筛选 "))])])],2);var s}}}),[["__scopeId","data-v-cb4b03e2"]]),ke={class:"tools-view"},be={class:"filters-bar"},Ce={class:"filters-content"},we={class:"filter-group search-group"},_e={class:"search-container"},Ue={class:"filter-group"},Oe={class:"filter-group"},xe={class:"filter-group"},Se={class:"view-options"},Fe={class:"results-info"},Le={class:"results-count"},Ee={class:"tools-content"},Ae={key:0,class:"loading-state"},Be={key:1,class:"error-state"},Ve={key:2,class:"tools-grid"},qe={class:"card-header"},He={class:"tool-icon"},Te=["onClick"],De={class:"card-content"},Qe={class:"tool-name"},Re={class:"tool-description"},je={class:"tool-tags"},ze={key:0,class:"tag more"},Ie={class:"card-footer"},$e={class:"tool-stats"},Pe={class:"stat"},Me={class:"stat"},Ne={class:"tool-actions"},We=["onClick"],Ge=["onClick"],Ke={key:3,class:"tools-list"},Je=["onClick"],Xe={class:"item-left"},Ye={class:"tool-icon"},Ze={class:"tool-info"},ea={class:"tool-name"},aa={class:"tool-description"},sa={class:"tool-meta"},la={class:"category"},ta={class:"clicks"},ra={class:"item-right"},ca={class:"tool-tags"},oa={class:"tool-actions"},ia=["onClick"],na={key:4,class:"empty-state"},ua={key:0},va={key:1},da={key:2},pa={class:"empty-actions"},ga=j(s({__name:"ToolsView",setup(s){const y=I(),b=z(),C=R(),{searchQuery:w,filters:_,searchResults:U,popularSearches:O,searchHistory:x,search:j,clearSearch:P}=$(),M=e(),N=e("all"),W=e("name"),G=e(!1),K=e(!1),J=e("grid"),X=a(()=>{if(w.value.trim())return U.value.map(e=>e.item);let e=C.filteredTools;return K.value&&(e=e.filter(e=>e.isFavorite)),e=[...e].sort((e,a)=>{switch(W.value){case"click_count":return a.clickCount-e.clickCount;case"created_at":return new Date(a.createdAt).getTime()-new Date(e.createdAt).getTime();default:return e.name.localeCompare(a.name)}}),e}),Y=()=>{C.setSearchQuery(w.value)},Z=e=>{Object.assign(_,e)},ee=e=>{j(e),G.value=!1},ae=async e=>{if(console.log("点击工具:",e.name,"URL:",e.url),!e.url||""===e.url.trim())return console.warn("工具URL为空:",e),void alert("该工具暂无可用链接");try{let a=e.url.trim();a.startsWith("http://")||a.startsWith("https://")||(a="https://"+a),await C.incrementClickCount(e.id),window.open(a,"_blank","noopener,noreferrer")}catch(a){console.error("打开链接失败:",a),alert("无法打开该链接，请检查URL是否正确")}},se=async()=>{C.clearError(),await C.initialize()};return l(()=>y.query,e=>{e.category&&"string"==typeof e.category&&(N.value=e.category),e.search&&"string"==typeof e.search&&(w.value=e.search,C.setSearchQuery(e.search))},{immediate:!0}),l(N,e=>{C.setSelectedCategory(e);const a={...y.query};"all"===e?delete a.category:a.category=e,b.replace({query:a})}),l(w,e=>{const a={...y.query};e?a.search=e:delete a.search,b.replace({query:a})}),L(async()=>{C.initialized||await C.initialize()}),(e,a)=>(F(),t("div",ke,[c("div",be,[c("div",Ce,[c("div",we,[c("div",_e,[o(i(S),{class:"search-icon"}),v(c("input",{ref_key:"searchInput",ref:M,"onUpdate:modelValue":a[0]||(a[0]=e=>E(w)?w.value=e:null),type:"text",placeholder:"搜索工具...",class:"search-input",onInput:Y,onFocus:a[1]||(a[1]=e=>G.value=!1)},null,544),[[m,i(w)]]),i(w)?(F(),t("button",{key:0,class:"clear-search",onClick:a[2]||(a[2]=(...e)=>i(P)&&i(P)(...e))},[o(i(n),{class:"icon"})])):u("",!0),c("button",{onClick:a[3]||(a[3]=e=>G.value=!G.value),class:r(["advanced-search-button",{active:G.value}]),title:"高级搜索"},[o(i(A),{class:"icon"})],2),o(fe,{"is-open":G.value,filters:i(_),"search-history":i(x),"popular-searches":i(O),onClose:a[4]||(a[4]=e=>G.value=!1),"onUpdate:filters":Z,onSearch:ee},null,8,["is-open","filters","search-history","popular-searches"])])]),c("div",Ue,[a[12]||(a[12]=c("label",{class:"filter-label"},"排序：",-1)),v(c("select",{"onUpdate:modelValue":a[5]||(a[5]=e=>W.value=e),class:"filter-select"},a[11]||(a[11]=[c("option",{value:"name"},"名称",-1),c("option",{value:"click_count"},"热度",-1),c("option",{value:"created_at"},"最新",-1)]),512),[[d,W.value]])]),c("div",Oe,[c("button",{class:r(["filter-button",{active:K.value}]),onClick:a[6]||(a[6]=e=>K.value=!K.value)},[o(i(k),{class:"icon"}),a[13]||(a[13]=f(" 只看收藏 "))],2)]),c("div",xe,[c("div",Se,[c("button",{class:r(["view-button",{active:"grid"===J.value}]),onClick:a[7]||(a[7]=e=>J.value="grid")},[o(i(B),{class:"icon"})],2),c("button",{class:r(["view-button",{active:"list"===J.value}]),onClick:a[8]||(a[8]=e=>J.value="list")},[o(i(V),{class:"icon"})],2)])]),c("div",Fe,[c("span",Le," 找到 "+h(X.value.length)+" 个工具 ",1)])])]),c("div",Ee,[i(C).loading?(F(),t("div",Ae,a[14]||(a[14]=[c("div",{class:"loading-spinner"},null,-1),c("p",null,"正在加载工具...",-1)]))):i(C).error?(F(),t("div",Be,[a[15]||(a[15]=c("div",{class:"error-icon"},"❌",-1)),a[16]||(a[16]=c("h3",null,"加载失败",-1)),c("p",null,h(i(C).error),1),c("button",{class:"retry-button",onClick:se},"重试")])):X.value.length>0&&"grid"===J.value?(F(),t("div",Ve,[(F(!0),t(p,null,g(X.value,e=>{var a;return F(),t("div",{key:e.id,class:"tool-card"},[c("div",qe,[c("div",He,h(e.icon||"🔧"),1),c("button",{class:r(["favorite-button",{active:e.isFavorite}]),onClick:q(a=>i(C).toggleFavorite(e.id),["stop"])},[o(i(k),{class:"icon"})],10,Te)]),c("div",De,[c("h3",Qe,h(e.name),1),c("p",Re,h(e.description),1),c("div",je,[(F(!0),t(p,null,g((e.tags||[]).slice(0,3),e=>(F(),t("span",{key:e,class:"tag"},h(e),1))),128)),(e.tags||[]).length>3?(F(),t("span",ze," +"+h((e.tags||[]).length-3),1)):u("",!0)])]),c("div",Ie,[c("div",$e,[c("span",Pe,[o(i(H),{class:"stat-icon"}),f(" "+h(e.click_count||0),1)]),c("span",Me,[o(i(T),{class:"stat-icon"}),f(" "+h((null==(a=e.categories)?void 0:a.name)||"未分类"),1)])]),c("div",Ne,[c("button",{onClick:q(a=>{return s=e.id,void b.push(`/tools/${s}`);var s},["stop"]),class:"detail-button",title:"查看详情"},[o(i(D),{class:"icon"})],8,We),c("button",{onClick:q(a=>ae(e),["stop"]),class:"visit-button",title:"访问工具"},[o(i(Q),{class:"icon"})],8,Ge)])])])}),128))])):X.value.length>0&&"list"===J.value?(F(),t("div",Ke,[(F(!0),t(p,null,g(X.value,e=>(F(),t("div",{key:e.id,class:"tool-item",onClick:a=>ae(e)},[c("div",Xe,[c("div",Ye,h(e.icon||"🔧"),1),c("div",Ze,[c("h3",ea,h(e.name),1),c("p",aa,h(e.description),1),c("div",sa,[c("span",la,h(e.category.name),1),a[17]||(a[17]=c("span",{class:"separator"},"•",-1)),c("span",ta,h(e.clickCount)+" 次访问",1)])])]),c("div",ra,[c("div",ca,[(F(!0),t(p,null,g((e.tags||[]).slice(0,2),e=>(F(),t("span",{key:e,class:"tag"},h(e),1))),128))]),c("div",oa,[c("button",{class:r(["favorite-button",{active:e.isFavorite}]),onClick:q(a=>i(C).toggleFavorite(e.id),["stop"])},[o(i(k),{class:"icon"})],10,ia),o(i(Q),{class:"external-icon"})])])],8,Je))),128))])):(F(),t("div",na,[a[18]||(a[18]=c("div",{class:"empty-icon"},"🔍",-1)),a[19]||(a[19]=c("h3",null,"未找到相关工具",-1)),i(w)?(F(),t("p",ua,' 没有找到包含 "'+h(i(w))+'" 的工具，尝试使用其他关键词搜索 ',1)):"all"!==N.value?(F(),t("p",va," 该分类下暂无工具，请选择其他分类 ")):(F(),t("p",da,"暂无工具数据，请稍后再试")),c("div",pa,[i(w)?(F(),t("button",{key:0,class:"btn btn-primary",onClick:a[9]||(a[9]=(...e)=>i(P)&&i(P)(...e))}," 清除搜索条件 ")):u("",!0),"all"!==N.value?(F(),t("button",{key:1,class:"btn btn-secondary",onClick:a[10]||(a[10]=e=>N.value="all")}," 查看全部分类 ")):u("",!0)])]))])]))}}),[["__scopeId","data-v-290a208e"]]);export{ga as default};
