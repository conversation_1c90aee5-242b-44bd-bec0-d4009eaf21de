-- 种子数据

-- 插入默认分类
INSERT INTO categories (id, name, description, icon, color, sort_order) VALUES
    ('550e8400-e29b-41d4-a716-446655440001', '开发工具', '编程和开发相关的工具', '💻', '#0078d4', 1),
    ('550e8400-e29b-41d4-a716-446655440002', '设计工具', '设计和创意相关的工具', '🎨', '#7b1fa2', 2),
    ('550e8400-e29b-41d4-a716-446655440003', '办公工具', '办公和生产力工具', '📊', '#388e3c', 3),
    ('550e8400-e29b-41d4-a716-446655440004', '学习工具', '学习和教育相关的工具', '📚', '#f57c00', 4),
    ('550e8400-e29b-41d4-a716-446655440005', '娱乐工具', '娱乐和休闲工具', '🎮', '#e91e63', 5),
    ('550e8400-e29b-41d4-a716-446655440006', '实用工具', '日常实用工具', '🔧', '#607d8b', 6);

-- 插入产品分类
INSERT INTO product_categories (id, name, description, icon, color, sort_order) VALUES
    ('650e8400-e29b-41d4-a716-446655440001', '开发软件', '编程开发相关软件产品', '💻', '#0078d4', 1),
    ('650e8400-e29b-41d4-a716-446655440002', '设计软件', '设计创意相关软件产品', '🎨', '#7b1fa2', 2),
    ('650e8400-e29b-41d4-a716-446655440003', '办公软件', '办公生产力软件产品', '📊', '#388e3c', 3),
    ('650e8400-e29b-41d4-a716-446655440004', '教育软件', '学习教育相关软件产品', '📚', '#f57c00', 4),
    ('650e8400-e29b-41d4-a716-446655440005', '模板资源', '各类模板和资源包', '📄', '#9c27b0', 5),
    ('650e8400-e29b-41d4-a716-446655440006', '插件扩展', '浏览器插件和软件扩展', '🔌', '#795548', 6);

-- 插入示例工具
INSERT INTO tools (id, name, description, url, icon, category_id, is_featured, sort_order) VALUES
    ('750e8400-e29b-41d4-a716-446655440001', 'Visual Studio Code', '强大的代码编辑器，支持多种编程语言', 'https://code.visualstudio.com', '💻', '550e8400-e29b-41d4-a716-446655440001', true, 1),
    ('750e8400-e29b-41d4-a716-446655440002', 'GitHub', '全球最大的代码托管平台', 'https://github.com', '🐙', '550e8400-e29b-41d4-a716-446655440001', true, 2),
    ('750e8400-e29b-41d4-a716-446655440003', 'Figma', '协作式界面设计工具', 'https://figma.com', '🎨', '550e8400-e29b-41d4-a716-446655440002', true, 3),
    ('750e8400-e29b-41d4-a716-446655440004', 'Notion', '全能的笔记和协作工具', 'https://notion.so', '📝', '550e8400-e29b-41d4-a716-446655440003', true, 4),
    ('750e8400-e29b-41d4-a716-446655440005', 'ChatGPT', 'AI 对话助手', 'https://chat.openai.com', '🤖', '550e8400-e29b-41d4-a716-446655440004', true, 5),
    ('750e8400-e29b-41d4-a716-446655440006', 'Canva', '在线设计平台', 'https://canva.com', '🖼️', '550e8400-e29b-41d4-a716-446655440002', false, 6),
    ('750e8400-e29b-41d4-a716-446655440007', 'Trello', '项目管理和协作工具', 'https://trello.com', '📋', '550e8400-e29b-41d4-a716-446655440003', false, 7),
    ('750e8400-e29b-41d4-a716-446655440008', 'Stack Overflow', '程序员问答社区', 'https://stackoverflow.com', '📚', '550e8400-e29b-41d4-a716-446655440004', false, 8);

-- 插入示例产品
INSERT INTO products (id, name, description, short_description, price, original_price, category_id, images, features, is_featured, sort_order) VALUES
    ('850e8400-e29b-41d4-a716-446655440001', '高效办公套件', '提升办公效率的完整解决方案，包含文档处理、项目管理、时间追踪等功能。适合个人和团队使用，支持多平台同步。', '办公效率提升工具包', 299.00, 399.00, '650e8400-e29b-41d4-a716-446655440003', ARRAY['/images/office-suite-1.jpg', '/images/office-suite-2.jpg'], ARRAY['文档处理', '项目管理', '时间追踪', '团队协作', '多平台同步'], true, 1),
    ('850e8400-e29b-41d4-a716-446655440002', '设计师工具包', '专业设计师必备的工具集合，包含图标库、字体包、设计模板等资源。', '设计资源大礼包', 199.00, 299.00, '650e8400-e29b-41d4-a716-446655440002', ARRAY['/images/design-kit-1.jpg', '/images/design-kit-2.jpg'], ARRAY['10000+ 图标', '100+ 字体', '设计模板', '配色方案', '商用授权'], true, 2),
    ('850e8400-e29b-41d4-a716-446655440003', '编程学习课程', '从零基础到高级的完整编程学习路径，包含视频教程、实战项目和在线答疑。', '全栈开发学习课程', 599.00, 799.00, '650e8400-e29b-41d4-a716-446655440004', ARRAY['/images/coding-course-1.jpg', '/images/coding-course-2.jpg'], ARRAY['100+ 小时视频', '20+ 实战项目', '在线答疑', '学习证书', '终身更新'], true, 3),
    ('850e8400-e29b-41d4-a716-446655440004', 'Vue.js 组件库', '基于 Vue 3 的企业级组件库，包含 50+ 高质量组件和完整的设计系统。', 'Vue 3 企业组件库', 399.00, 499.00, '650e8400-e29b-41d4-a716-446655440001', ARRAY['/images/vue-components-1.jpg', '/images/vue-components-2.jpg'], ARRAY['50+ 组件', 'TypeScript 支持', '响应式设计', '主题定制', '完整文档'], false, 4),
    ('850e8400-e29b-41d4-a716-446655440005', '网站模板合集', '精美的网站模板合集，包含企业官网、电商网站、个人博客等多种类型。', '多用途网站模板', 159.00, 199.00, '650e8400-e29b-41d4-a716-446655440005', ARRAY['/images/templates-1.jpg', '/images/templates-2.jpg'], ARRAY['20+ 模板', '响应式设计', '易于定制', 'SEO 优化', '商用授权'], false, 5);

-- 插入标签
INSERT INTO tags (id, name, color) VALUES
    ('950e8400-e29b-41d4-a716-446655440001', '免费', '#4caf50'),
    ('950e8400-e29b-41d4-a716-446655440002', '开源', '#2196f3'),
    ('950e8400-e29b-41d4-a716-446655440003', '在线工具', '#ff9800'),
    ('950e8400-e29b-41d4-a716-446655440004', '桌面应用', '#9c27b0'),
    ('950e8400-e29b-41d4-a716-446655440005', '移动应用', '#607d8b'),
    ('950e8400-e29b-41d4-a716-446655440006', 'AI 工具', '#e91e63'),
    ('950e8400-e29b-41d4-a716-446655440007', '协作工具', '#795548'),
    ('950e8400-e29b-41d4-a716-446655440008', '学习资源', '#3f51b5');

-- 插入工具标签关联
INSERT INTO tool_tags (tool_id, tag_id) VALUES
    ('750e8400-e29b-41d4-a716-446655440001', '950e8400-e29b-41d4-a716-446655440001'), -- VS Code - 免费
    ('750e8400-e29b-41d4-a716-446655440001', '950e8400-e29b-41d4-a716-446655440002'), -- VS Code - 开源
    ('750e8400-e29b-41d4-a716-446655440001', '950e8400-e29b-41d4-a716-446655440004'), -- VS Code - 桌面应用
    ('750e8400-e29b-41d4-a716-446655440002', '950e8400-e29b-41d4-a716-446655440001'), -- GitHub - 免费
    ('750e8400-e29b-41d4-a716-446655440002', '950e8400-e29b-41d4-a716-446655440003'), -- GitHub - 在线工具
    ('750e8400-e29b-41d4-a716-446655440002', '950e8400-e29b-41d4-a716-446655440007'), -- GitHub - 协作工具
    ('750e8400-e29b-41d4-a716-446655440003', '950e8400-e29b-41d4-a716-446655440003'), -- Figma - 在线工具
    ('750e8400-e29b-41d4-a716-446655440003', '950e8400-e29b-41d4-a716-446655440007'), -- Figma - 协作工具
    ('750e8400-e29b-41d4-a716-446655440004', '950e8400-e29b-41d4-a716-446655440003'), -- Notion - 在线工具
    ('750e8400-e29b-41d4-a716-446655440004', '950e8400-e29b-41d4-a716-446655440007'), -- Notion - 协作工具
    ('750e8400-e29b-41d4-a716-446655440005', '950e8400-e29b-41d4-a716-446655440003'), -- ChatGPT - 在线工具
    ('750e8400-e29b-41d4-a716-446655440005', '950e8400-e29b-41d4-a716-446655440006'), -- ChatGPT - AI 工具
    ('750e8400-e29b-41d4-a716-446655440006', '950e8400-e29b-41d4-a716-446655440003'), -- Canva - 在线工具
    ('750e8400-e29b-41d4-a716-446655440007', '950e8400-e29b-41d4-a716-446655440003'), -- Trello - 在线工具
    ('750e8400-e29b-41d4-a716-446655440007', '950e8400-e29b-41d4-a716-446655440007'), -- Trello - 协作工具
    ('750e8400-e29b-41d4-a716-446655440008', '950e8400-e29b-41d4-a716-446655440001'), -- Stack Overflow - 免费
    ('750e8400-e29b-41d4-a716-446655440008', '950e8400-e29b-41d4-a716-446655440003'), -- Stack Overflow - 在线工具
    ('750e8400-e29b-41d4-a716-446655440008', '950e8400-e29b-41d4-a716-446655440008'); -- Stack Overflow - 学习资源

-- 更新工具点击次数（模拟数据）
UPDATE tools SET click_count = 
    CASE id
        WHEN '750e8400-e29b-41d4-a716-446655440001' THEN 1250
        WHEN '750e8400-e29b-41d4-a716-446655440002' THEN 980
        WHEN '750e8400-e29b-41d4-a716-446655440003' THEN 756
        WHEN '750e8400-e29b-41d4-a716-446655440004' THEN 642
        WHEN '750e8400-e29b-41d4-a716-446655440005' THEN 523
        WHEN '750e8400-e29b-41d4-a716-446655440006' THEN 412
        WHEN '750e8400-e29b-41d4-a716-446655440007' THEN 356
        WHEN '750e8400-e29b-41d4-a716-446655440008' THEN 289
        ELSE click_count
    END;

-- 更新产品评分（模拟数据）
UPDATE products SET 
    average_rating = 
        CASE id
            WHEN '850e8400-e29b-41d4-a716-446655440001' THEN 4.5
            WHEN '850e8400-e29b-41d4-a716-446655440002' THEN 4.3
            WHEN '850e8400-e29b-41d4-a716-446655440003' THEN 4.7
            WHEN '850e8400-e29b-41d4-a716-446655440004' THEN 4.2
            WHEN '850e8400-e29b-41d4-a716-446655440005' THEN 4.0
            ELSE average_rating
        END,
    total_reviews = 
        CASE id
            WHEN '850e8400-e29b-41d4-a716-446655440001' THEN 128
            WHEN '850e8400-e29b-41d4-a716-446655440002' THEN 95
            WHEN '850e8400-e29b-41d4-a716-446655440003' THEN 167
            WHEN '850e8400-e29b-41d4-a716-446655440004' THEN 73
            WHEN '850e8400-e29b-41d4-a716-446655440005' THEN 52
            ELSE total_reviews
        END;
