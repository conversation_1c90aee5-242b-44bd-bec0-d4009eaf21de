import{d as a,r as s,c as l,o as t,m as e,q as i,y as o,a1 as n,N as c,u as r,aq as d,a0 as u,a6 as v,R as h,F as p,z as m,H as g,C as f,t as k,K as y,x as _,ar as b,G as C}from"./vendor-VsOxy-e0.js";import{u as w,a as z,b as x,c as R,_ as j}from"./index-DdsLZGoA.js";import{E}from"./EnhancedSearchBox-BmYSxjit.js";const F={class:"enhanced-home-view"},L={class:"hero-section"},U={class:"container"},q={class:"hero-content"},H={class:"search-section"},S={class:"main-content"},W={class:"container"},A={class:"content-layout"},B={class:"sidebar"},G={class:"sidebar-section"},I={class:"nav-links"},K={class:"sidebar-section"},N={class:"category-filters"},V=["onClick"],D={class:"tools-grid"},J=["onClick"],M=["onClick"],O={class:"tool-header"},P={class:"tool-icon"},Q={class:"tool-title"},T={class:"tool-subtitle"},X={class:"tool-body"},Y={class:"tool-description"},Z={class:"tool-tags"},$={key:0,class:"tool-tag more"},aa={key:0,class:"empty-state"},sa=j(a({__name:"EnhancedHomeView",setup(a){const j=w(),sa=z(),la=x(),ta=R(),ea=s(null),ia=s(null),oa=l(()=>sa.categories),na=l(()=>{if(ia.value)return ia.value.items||[];let a=j.tools;return ea.value&&(a=a.filter(a=>a.category_id===ea.value)),a}),ca=a=>{ia.value=a},ra=()=>{ia.value=null},da=()=>{ia.value=null,ea.value=null};return t(async()=>{j.initialized||await j.initialize(),sa.initialized||await sa.initialize()}),(a,s)=>{const l=u("router-link");return C(),e("div",F,[i("header",L,[i("div",U,[i("div",q,[s[0]||(s[0]=i("div",{class:"logo-section"},[i("div",{class:"logo-icon"},"🚀"),i("h1",{class:"main-title"},"高效工具导航站")],-1)),s[1]||(s[1]=i("p",{class:"tagline"},"精心挑选的优质工具，让您的工作效率倍增",-1)),i("div",H,[o(E,{placeholder:"搜索工具、分类或功能...","auto-focus":!1,onSearch:ca,onClear:ra})])])])]),i("main",S,[i("div",W,[i("div",A,[i("aside",B,[i("div",G,[s[5]||(s[5]=i("h3",null,"导航",-1)),i("nav",I,[o(l,{to:"/tools",class:"nav-link active"},{default:n(()=>[o(r(d),{class:"icon"}),s[2]||(s[2]=c(" 全部工具 "))]),_:1,__:[2]}),o(l,{to:"/favorites",class:"nav-link"},{default:n(()=>[o(r(v),{class:"icon"}),s[3]||(s[3]=c(" 我的收藏 "))]),_:1,__:[3]}),o(l,{to:"/products",class:"nav-link"},{default:n(()=>[o(r(h),{class:"icon"}),s[4]||(s[4]=c(" 我的产品 "))]),_:1,__:[4]})])]),i("div",K,[s[6]||(s[6]=i("h3",null,"分类",-1)),i("div",N,[(C(!0),e(p,null,m(oa.value,a=>(C(),e("button",{key:a.id,class:g(["category-tag",{active:ea.value===a.id}]),onClick:s=>{return l=a.id,void(ea.value=ea.value===l?null:l);var l}},f(a.name),11,V))),128))])])]),i("section",D,[(C(!0),e(p,null,m(na.value,(a,s)=>(C(),e("div",{key:a.id,class:"tool-card",style:y({"--index":s}),onClick:s=>(async a=>{if(console.log("点击工具:",a.name,"URL:",a.url),!a.url||""===a.url.trim())return console.warn("工具URL为空:",a),void alert("该工具暂无可用链接");try{let s=a.url.trim();s.startsWith("http://")||s.startsWith("https://")||(s="https://"+s),await j.incrementClickCount(a.id),window.open(s,"_blank","noopener,noreferrer")}catch(s){console.error("打开链接失败:",s),alert("无法打开该链接，请检查URL是否正确")}})(a)},[i("button",{class:g(["favorite-btn",{favorited:a.isFavorite}]),onClick:_(s=>(async a=>{la.isAuthenticated?await j.toggleFavorite(a.id):ta.push("/auth/login")})(a),["stop"])},[o(r(b),{class:"icon"})],10,M),i("div",O,[i("div",P,f(a.icon||"🔧"),1),i("h3",Q,f(a.name),1),i("p",T,f(a.short_description||a.description.slice(0,50)+"..."),1)]),i("div",X,[i("p",Y,f(a.description),1),i("div",Z,[(C(!0),e(p,null,m((a.tags||[]).slice(0,3),a=>(C(),e("span",{key:a,class:"tool-tag"},f(a),1))),128)),(a.tags||[]).length>3?(C(),e("span",$," +"+f((a.tags||[]).length-3),1)):k("",!0)])])],12,J))),128)),0===na.value.length?(C(),e("div",aa,[s[7]||(s[7]=i("div",{class:"empty-icon"},"🔍",-1)),s[8]||(s[8]=i("h3",null,"未找到相关工具",-1)),s[9]||(s[9]=i("p",null,"尝试使用其他关键词搜索，或浏览其他分类",-1)),i("button",{class:"empty-action",onClick:da}," 清除搜索条件 ")])):k("",!0)])])])]),s[10]||(s[10]=i("footer",{class:"footer"},[i("div",{class:"container"},[i("p",null,"© 2024 高效工具导航站 | 让您的工作更智能、更高效")])],-1))])}}}),[["__scopeId","data-v-c277f1a9"]]);export{sa as default};
