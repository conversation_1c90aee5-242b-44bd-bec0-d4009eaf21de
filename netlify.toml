[build]
  command = "npm cache clean --force && npm install --no-optional && npm run build"
  publish = "dist"

[build.environment]
  NODE_VERSION = "20"
  # 仅安装必要依赖，避免平台特定依赖问题
  NPM_CONFIG_OPTIONAL = "false"
  NPM_CONFIG_IGNORE_PLATFORM = "true"
  # Supabase 环境变量通过 Netlify 环境变量设置
  
# SPA重定向规则 - 所有请求重定向到index.html
[[redirects]]
  from = "/*"
  to = "/index.html"
  status = 200

# 域名重定向规则
[[redirects]]
  from = "http://ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true

[[redirects]]
  from = "http://www.ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true

[[redirects]]
  from = "https://www.ramusi.cn/*"
  to = "https://ramusi.cn/:splat"
  status = 301
  force = true
