import{s as r,c as t,_ as a}from"./index-DdsLZGoA.js";import{d as e,o as s,m as o,q as i,y as n,a1 as c,N as l,u,aX as d,a0 as w,ai as h,aT as f,ah as m,aY as v,aZ as _,_ as g,a_ as p,a4 as y,G as E}from"./vendor-VsOxy-e0.js";class U{static async getCurrentUser(){try{const{data:{user:t}}=await r.auth.getUser();if(!t)return null;const{data:a,error:e}=await r.from("user_profiles").select("*").eq("id",t.id).single();if(e){if("PGRST116"===e.code)return this.createUserProfile(t.id,t.email);throw e}return this.transformUser(a)}catch(t){return console.error("获取用户信息失败:",t),null}}static async getUserProfile(t){try{const{data:a,error:e}=await r.from("user_profiles").select("*").eq("id",t).single();if(e)throw e;return a?this.transformUser(a):null}catch(a){return console.error("获取用户资料失败:",a),null}}static async updateProfile(t,a){try{const e={full_name:a.fullName,username:a.username,bio:a.bio,website:a.website,location:a.location,updated_at:(new Date).toISOString()};if(a.avatar){const r=await this.uploadAvatar(t,a.avatar);e.avatar_url=r}const{data:s,error:o}=await r.from("user_profiles").update(e).eq("id",t).select("*").single();if(o)throw o;if(!s)throw new Error("更新用户资料失败");return this.transformUser(s)}catch(e){throw console.error("更新用户资料失败:",e),new Error("更新用户资料失败")}}static async createUserProfile(t,a){try{const e={id:t,email:a,role:"user",is_active:!0,email_verified:!1},{data:s,error:o}=await r.from("user_profiles").insert(e).select("*").single();if(o)throw o;if(!s)throw new Error("创建用户资料失败");return this.transformUser(s)}catch(e){throw console.error("创建用户资料失败:",e),new Error("创建用户资料失败")}}static async uploadAvatar(t,a){try{const e=a.name.split(".").pop(),s=`avatars/${`${t}-${Date.now()}.${e}`}`,{data:o}=await r.storage.from("avatars").list("",{search:t});if(o&&o.length>0){const t=o.map(r=>r.name);await r.storage.from("avatars").remove(t)}const{error:i}=await r.storage.from("avatars").upload(s,a);if(i)throw i;const{data:n}=r.storage.from("avatars").getPublicUrl(s);return n.publicUrl}catch(e){throw console.error("上传头像失败:",e),new Error("上传头像失败")}}static async updateLastLogin(t){try{const{error:a}=await r.from("user_profiles").update({last_login_at:(new Date).toISOString()}).eq("id",t);if(a)throw a}catch(a){console.error("更新最后登录时间失败:",a)}}static async checkUsernameAvailability(t,a){try{let e=r.from("user_profiles").select("id").eq("username",t);a&&(e=e.neq("id",a));const{data:s,error:o}=await e;if(o)throw o;return!s||0===s.length}catch(e){return console.error("检查用户名可用性失败:",e),!1}}static async getUserStats(t){try{const[a,e]=await Promise.all([r.from("favorites").select("tool_id, product_id").eq("user_id",t),r.from("orders").select("total_amount, status").eq("user_id",t)]),s=a.data||[],o=e.data||[],i=s.filter(r=>r.tool_id).length,n=s.filter(r=>r.product_id).length,c=o.length;return{favoriteToolsCount:i,favoriteProductsCount:n,ordersCount:c,totalSpent:o.filter(r=>"paid"===r.status).reduce((r,t)=>r+t.total_amount,0)}}catch(a){return console.error("获取用户统计信息失败:",a),{favoriteToolsCount:0,favoriteProductsCount:0,ordersCount:0,totalSpent:0}}}static async deleteAccount(t){try{await Promise.all([r.from("favorites").delete().eq("user_id",t),r.from("orders").delete().eq("user_id",t),r.from("user_profiles").delete().eq("id",t)]);const{error:a}=await r.auth.admin.deleteUser(t);if(a)throw a}catch(a){throw console.error("删除用户账户失败:",a),new Error("删除用户账户失败")}}static transformUser(r){return{id:r.id,email:r.email,username:r.username,fullName:r.full_name,avatarUrl:r.avatar_url,bio:r.bio,website:r.website,location:r.location,role:r.role,isActive:r.is_active,emailVerified:r.email_verified,createdAt:r.created_at,updatedAt:r.updated_at,lastLoginAt:r.last_login_at}}}class b{static async login(t){try{const{data:a,error:e}=await r.auth.signInWithPassword({email:t.email,password:t.password});if(e)throw e;if(!a.user)throw new Error("登录失败");await U.updateLastLogin(a.user.id);const s=await U.getCurrentUser();if(!s)throw new Error("获取用户信息失败");return{user:s,session:a.session}}catch(a){throw console.error("登录失败:",a),new Error(a instanceof Error?a.message:"登录失败")}}static async register(t){try{if(t.username){if(!(await U.checkUsernameAvailability(t.username)))throw new Error("用户名已被使用")}const{data:a,error:e}=await r.auth.signUp({email:t.email,password:t.password,options:{data:{full_name:t.fullName,username:t.username}}});if(e)throw e;if(!a.user)throw new Error("注册失败");const s=await U.createUserProfile(a.user.id,t.email);if(t.fullName||t.username){return{user:await U.updateProfile(a.user.id,{fullName:t.fullName,username:t.username}),session:a.session}}return{user:s,session:a.session}}catch(a){throw console.error("注册失败:",a),new Error(a instanceof Error?a.message:"注册失败")}}static async logout(){try{const{error:t}=await r.auth.signOut();if(t)throw t}catch(t){throw console.error("登出失败:",t),new Error("登出失败")}}static async forgotPassword(t){try{const{error:a}=await r.auth.resetPasswordForEmail(t,{redirectTo:`${window.location.origin}/auth/reset-password`});if(a)throw a}catch(a){throw console.error("发送重置密码邮件失败:",a),new Error("发送重置密码邮件失败")}}static async resetPassword(t){try{const{error:a}=await r.auth.updateUser({password:t});if(a)throw a}catch(a){throw console.error("重置密码失败:",a),new Error("重置密码失败")}}static async changePassword(t){try{const{error:a}=await r.auth.updateUser({password:t});if(a)throw a}catch(a){throw console.error("更改密码失败:",a),new Error("更改密码失败")}}static async updateEmail(t){try{const{error:a}=await r.auth.updateUser({email:t});if(a)throw a}catch(a){throw console.error("更新邮箱失败:",a),new Error("更新邮箱失败")}}static async verifyEmail(t,a){try{const{error:e}=await r.auth.verifyOtp({token_hash:t,type:a});if(e)throw e}catch(e){throw console.error("验证邮箱失败:",e),new Error("验证邮箱失败")}}static async resendVerificationEmail(){try{const{data:{user:t}}=await r.auth.getUser();if(!t)throw new Error("用户未登录");const{error:a}=await r.auth.resend({type:"signup",email:t.email});if(a)throw a}catch(t){throw console.error("重新发送验证邮件失败:",t),new Error("重新发送验证邮件失败")}}static async getSession(){try{const{data:{session:t}}=await r.auth.getSession();return t}catch(t){return console.error("获取会话失败:",t),null}}static async refreshSession(){try{const{data:t,error:a}=await r.auth.refreshSession();if(a)throw a;return t.session}catch(t){throw console.error("刷新会话失败:",t),new Error("刷新会话失败")}}static async isAuthenticated(){try{const{data:{user:t}}=await r.auth.getUser();return!!t}catch(t){return!1}}static onAuthStateChange(t){return r.auth.onAuthStateChange(t)}static async signInWithGoogle(){try{const{error:t}=await r.auth.signInWithOAuth({provider:"google",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(t)throw t}catch(t){throw console.error("Google 登录失败:",t),new Error("Google 登录失败")}}static async signInWithGitHub(){try{const{error:t}=await r.auth.signInWithOAuth({provider:"github",options:{redirectTo:`${window.location.origin}/auth/callback`}});if(t)throw t}catch(t){throw console.error("GitHub 登录失败:",t),new Error("GitHub 登录失败")}}static async deleteAccount(){try{const{data:{user:t}}=await r.auth.getUser();if(!t)throw new Error("用户未登录");await U.deleteAccount(t.id)}catch(t){throw console.error("删除账户失败:",t),new Error("删除账户失败")}}}const A={class:"admin-view"},P={class:"admin-content"},S={class:"container"},q={class:"admin-layout"},C={class:"admin-nav"},G={class:"admin-main"},I=a(e({__name:"AdminView",setup(r){const a=t(),e=async()=>{try{await b.logout(),a.push("/")}catch(r){console.error("退出登录失败:",r)}};return s(()=>{(async()=>{try{const r=await U.getCurrentUser();if(!r||"admin"!==r.role&&"super_admin"!==r.role)return console.warn("管理员权限检查失败，重定向到首页"),void a.push("/")}catch(r){console.error("检查管理员权限失败:",r),a.push("/auth/login")}})()}),(r,t)=>{const a=w("router-link"),s=w("router-view");return E(),o("div",A,[t[10]||(t[10]=i("div",{class:"admin-header"},[i("div",{class:"container"},[i("h1",null,"管理后台"),i("p",null,"系统管理和数据统计")])],-1)),i("div",P,[i("div",S,[i("div",q,[i("nav",C,[n(a,{to:"/admin/dashboard",class:"nav-item","active-class":"active"},{default:c(()=>[n(u(d),{class:"nav-icon"}),t[0]||(t[0]=l(" 仪表盘 "))]),_:1,__:[0]}),n(a,{to:"/admin/tools",class:"nav-item","active-class":"active"},{default:c(()=>[n(u(h),{class:"nav-icon"}),t[1]||(t[1]=l(" 工具管理 "))]),_:1,__:[1]}),n(a,{to:"/admin/products",class:"nav-item","active-class":"active"},{default:c(()=>[n(u(f),{class:"nav-icon"}),t[2]||(t[2]=l(" 产品管理 "))]),_:1,__:[2]}),n(a,{to:"/admin/users",class:"nav-item","active-class":"active"},{default:c(()=>[n(u(m),{class:"nav-icon"}),t[3]||(t[3]=l(" 用户管理 "))]),_:1,__:[3]}),n(a,{to:"/admin/orders",class:"nav-item","active-class":"active"},{default:c(()=>[n(u(v),{class:"nav-icon"}),t[4]||(t[4]=l(" 订单管理 "))]),_:1,__:[4]}),n(a,{to:"/admin/local",class:"nav-item","active-class":"active"},{default:c(()=>[n(u(_),{class:"nav-icon"}),t[5]||(t[5]=l(" 本地管理 "))]),_:1,__:[5]}),n(a,{to:"/admin/settings",class:"nav-item","active-class":"active"},{default:c(()=>[n(u(g),{class:"nav-icon"}),t[6]||(t[6]=l(" 系统设置 "))]),_:1,__:[6]}),t[9]||(t[9]=i("div",{class:"nav-divider"},null,-1)),n(a,{to:"/",class:"nav-item"},{default:c(()=>[n(u(p),{class:"nav-icon"}),t[7]||(t[7]=l(" 返回首页 "))]),_:1,__:[7]}),i("button",{class:"nav-item logout-btn",onClick:e},[n(u(y),{class:"nav-icon"}),t[8]||(t[8]=l(" 退出登录 "))])]),i("main",G,[n(s)])])])])])}}}),[["__scopeId","data-v-64a02da0"]]);export{I as default};
