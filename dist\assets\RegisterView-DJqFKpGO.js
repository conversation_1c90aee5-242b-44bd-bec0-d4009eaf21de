import{d as a,r as e,c as s,m as l,q as r,t,A as o,B as d,aV as u,v as i,u as n,aQ as c,aW as v,K as p,H as m,C as f,N as w,E as g,x as h,y as b,a1 as y,a0 as k,G as x}from"./vendor-VsOxy-e0.js";import{c as P,_ as T}from"./index-DdsLZGoA.js";const _={class:"register-view"},V={class:"form-group"},N=["disabled"],q={class:"form-group"},U=["disabled"],A={class:"form-group"},j={class:"password-input"},z=["type","disabled"],C={class:"password-strength"},E={class:"strength-bar"},Z={class:"strength-text"},B={class:"form-group"},G=["disabled"],H={key:0,class:"error-hint"},I={class:"form-group"},K={class:"checkbox-label"},Q=["disabled"],R={key:0,class:"loading-spinner"},S={key:0,class:"error-message"},W={class:"register-footer"},D=T(a({__name:"RegisterView",setup(a){const T=P(),D=e(!1),F=e(null),J=e(!1),L=e({fullName:"",email:"",password:"",confirmPassword:"",agreeToTerms:!1}),M=s(()=>{const a=L.value.password;if(!a)return{class:"",width:"0%",text:""};let e=0;return a.length>=8&&e++,/[a-z]/.test(a)&&e++,/[A-Z]/.test(a)&&e++,/[0-9]/.test(a)&&e++,/[^A-Za-z0-9]/.test(a)&&e++,e<2?{class:"weak",width:"20%",text:"弱"}:e<3?{class:"fair",width:"40%",text:"一般"}:e<4?{class:"good",width:"60%",text:"良好"}:e<5?{class:"strong",width:"80%",text:"强"}:{class:"very-strong",width:"100%",text:"很强"}}),O=s(()=>L.value.email&&L.value.password&&L.value.password===L.value.confirmPassword&&L.value.agreeToTerms&&L.value.password.length>=8),X=async()=>{try{D.value=!0,F.value=null,await new Promise(a=>setTimeout(a,1500)),console.log("注册成功:",L.value.email),T.push("/auth/login")}catch(a){F.value=a instanceof Error?a.message:"注册失败，请重试"}finally{D.value=!1}};return(a,e)=>{const s=k("router-link");return x(),l("div",_,[e[17]||(e[17]=r("div",{class:"register-header"},[r("h1",null,"注册"),r("p",null,"创建您的账户，开始使用工具导航站")],-1)),r("form",{class:"register-form",onSubmit:h(X,["prevent"])},[r("div",V,[e[6]||(e[6]=r("label",{for:"fullName"},"姓名",-1)),o(r("input",{id:"fullName","onUpdate:modelValue":e[0]||(e[0]=a=>L.value.fullName=a),type:"text",placeholder:"请输入您的姓名",disabled:D.value},null,8,N),[[d,L.value.fullName]])]),r("div",q,[e[7]||(e[7]=r("label",{for:"email"},"邮箱地址 *",-1)),o(r("input",{id:"email","onUpdate:modelValue":e[1]||(e[1]=a=>L.value.email=a),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:D.value},null,8,U),[[d,L.value.email]])]),r("div",A,[e[8]||(e[8]=r("label",{for:"password"},"密码 *",-1)),r("div",j,[o(r("input",{id:"password","onUpdate:modelValue":e[2]||(e[2]=a=>L.value.password=a),type:J.value?"text":"password",required:"",placeholder:"请输入密码（至少8位）",disabled:D.value},null,8,z),[[u,L.value.password]]),r("button",{type:"button",class:"password-toggle",onClick:e[3]||(e[3]=a=>J.value=!J.value)},[J.value?(x(),i(n(v),{key:1,class:"icon"})):(x(),i(n(c),{key:0,class:"icon"}))])]),r("div",C,[r("div",E,[r("div",{class:m(["strength-fill",M.value.class]),style:p({width:M.value.width})},null,6)]),r("span",Z,f(M.value.text),1)])]),r("div",B,[e[9]||(e[9]=r("label",{for:"confirmPassword"},"确认密码 *",-1)),o(r("input",{id:"confirmPassword","onUpdate:modelValue":e[4]||(e[4]=a=>L.value.confirmPassword=a),type:"password",required:"",placeholder:"请再次输入密码",disabled:D.value},null,8,G),[[d,L.value.confirmPassword]]),L.value.confirmPassword&&L.value.password!==L.value.confirmPassword?(x(),l("div",H," 密码不匹配 ")):t("",!0)]),r("div",I,[r("label",K,[o(r("input",{"onUpdate:modelValue":e[5]||(e[5]=a=>L.value.agreeToTerms=a),type:"checkbox",required:""},null,512),[[g,L.value.agreeToTerms]]),e[10]||(e[10]=r("span",{class:"checkmark"},null,-1)),e[11]||(e[11]=w(" 我已阅读并同意 ")),e[12]||(e[12]=r("a",{href:"#",class:"terms-link"},"服务条款",-1)),e[13]||(e[13]=w(" 和 ")),e[14]||(e[14]=r("a",{href:"#",class:"terms-link"},"隐私政策",-1))])]),r("button",{type:"submit",class:"register-btn",disabled:D.value||!O.value},[D.value?(x(),l("div",R)):t("",!0),r("span",null,f(D.value?"注册中...":"注册"),1)],8,Q),F.value?(x(),l("div",S,f(F.value),1)):t("",!0)],32),r("div",W,[r("p",null,[e[16]||(e[16]=w(" 已有账户？ ")),b(s,{to:"/auth/login",class:"login-link"},{default:y(()=>e[15]||(e[15]=[w("立即登录")])),_:1,__:[15]})])])])}}}),[["__scopeId","data-v-cace2648"]]);export{D as default};
