import{d as e,m as t,q as s,y as a,u as r,X as l,t as d,R as c,C as o,H as n,N as i,af as u,W as m,U as p,b1 as x,b3 as g,aW as y,b4 as b,F as v,z as f,G as h,r as w,b as k,c as _,o as j,v as C,b0 as $,a1 as D,aK as T,a0 as Z,A as U,a2 as V,B as L,D as z,a8 as A,aI as I}from"./vendor-VsOxy-e0.js";const M={class:"fixed inset-0 z-50 overflow-y-auto"},E={class:"flex items-center justify-center min-h-screen px-4 pt-4 pb-20 text-center sm:block sm:p-0"},N={class:"inline-block w-full max-w-4xl p-6 my-8 overflow-hidden text-left align-middle transition-all transform bg-white shadow-xl rounded-lg"},S={class:"flex items-center justify-between pb-4 border-b border-gray-200"},P={class:"mt-6"},R={class:"grid grid-cols-1 lg:grid-cols-2 gap-8"},W={class:"mb-6"},q={key:0,class:"space-y-3"},B=["src","alt"],F={key:1,class:"w-full h-48 bg-gray-100 rounded-lg border border-gray-200 flex items-center justify-center"},G={class:"space-y-4"},H={class:"mt-1 text-sm text-gray-900"},K={class:"mt-1 text-sm text-gray-900"},J={class:"grid grid-cols-2 gap-4"},O={class:"mt-1 text-sm text-gray-900"},Q={class:"mt-1 text-sm text-gray-900"},X={class:"mt-1 text-sm text-gray-900"},Y=["href"],ee={key:1,class:"text-gray-500"},te={class:"bg-gray-50 rounded-lg p-4 mb-6"},se={class:"space-y-3"},ae={class:"flex justify-between"},re={class:"flex justify-between"},le={class:"text-sm text-gray-900"},de={class:"flex justify-between"},ce={class:"text-sm text-gray-900"},oe={class:"flex justify-between"},ne={class:"text-sm text-gray-900"},ie={key:0,class:"bg-yellow-50 border border-yellow-200 rounded-lg p-4 mb-6"},ue={class:"text-sm font-medium text-yellow-800 mb-3 flex items-center"},me={class:"space-y-3"},pe={class:"space-y-3"},xe={class:"mt-6 pt-6 border-t border-gray-200"},ge={key:0,class:"mt-8 pt-6 border-t border-gray-200"},ye={class:"prose prose-sm max-w-none"},be={class:"text-sm text-gray-700 whitespace-pre-wrap"},ve={key:1,class:"mt-6 pt-6 border-t border-gray-200"},fe={class:"flex flex-wrap gap-2"},he={class:"mt-8 pt-6 border-t border-gray-200 flex justify-end space-x-3"},we=e({__name:"ProductDetailModal",props:{product:{}},emits:["close","approve","reject","edit","publish","unpublish","delete"],setup(e,{emit:w}){const k=e,_=w,j=[{id:"1",name:"开发工具"},{id:"2",name:"设计工具"},{id:"3",name:"办公软件"},{id:"4",name:"学习资源"},{id:"5",name:"其他工具"}],C=e=>{const t=j.find(t=>t.id===e);return(null==t?void 0:t.name)||"未知分类"},$=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":case"published":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},D=e=>{switch(e){case"pending":return"待审核";case"approved":return"已通过";case"published":return"已发布";case"rejected":return"已拒绝";default:return"未知"}},T=e=>new Date(e).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}),Z=()=>{confirm(`确定要通过产品"${k.product.name}"的审核吗？`)&&_("approve",k.product)},U=()=>{prompt(`请输入拒绝产品"${k.product.name}"的原因：`)&&_("reject",k.product)},V=()=>{_("edit",k.product)},L=()=>{confirm(`确定要发布产品"${k.product.name}"吗？`)&&_("publish",k.product)},z=()=>{confirm(`确定要取消发布产品"${k.product.name}"吗？`)&&_("unpublish",k.product)},A=()=>{confirm(`确定要删除产品"${k.product.name}"吗？此操作不可恢复。`)&&_("delete",k.product)};return(e,w)=>(h(),t("div",M,[s("div",E,[s("div",{class:"fixed inset-0 transition-opacity bg-gray-500 bg-opacity-75",onClick:w[0]||(w[0]=t=>e.$emit("close"))}),s("div",N,[s("div",S,[w[3]||(w[3]=s("h3",{class:"text-lg font-medium text-gray-900"},"产品详情",-1)),s("button",{onClick:w[1]||(w[1]=t=>e.$emit("close")),class:"text-gray-400 hover:text-gray-600"},[a(r(l),{class:"w-6 h-6"})])]),s("div",P,[s("div",R,[s("div",null,[s("div",W,[w[4]||(w[4]=s("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"产品图片",-1)),e.product.image_url?(h(),t("div",q,[s("img",{src:e.product.image_url,alt:e.product.name,class:"w-full h-48 object-cover rounded-lg border border-gray-200"},null,8,B)])):(h(),t("div",F,[a(r(c),{class:"w-12 h-12 text-gray-400"})]))]),s("div",G,[s("div",null,[w[5]||(w[5]=s("label",{class:"block text-sm font-medium text-gray-700"},"产品名称",-1)),s("p",H,o(e.product.name),1)]),s("div",null,[w[6]||(w[6]=s("label",{class:"block text-sm font-medium text-gray-700"},"产品描述",-1)),s("p",K,o(e.product.description),1)]),s("div",J,[s("div",null,[w[7]||(w[7]=s("label",{class:"block text-sm font-medium text-gray-700"},"价格",-1)),s("p",O,o(0===e.product.price?"免费":`¥${e.product.price}`),1)]),s("div",null,[w[8]||(w[8]=s("label",{class:"block text-sm font-medium text-gray-700"},"分类",-1)),s("p",Q,o(C(e.product.category_id)),1)])]),s("div",null,[w[9]||(w[9]=s("label",{class:"block text-sm font-medium text-gray-700"},"产品链接",-1)),s("p",X,[e.product.url?(h(),t("a",{key:0,href:e.product.url,target:"_blank",class:"text-blue-600 hover:text-blue-800 underline"},o(e.product.url),9,Y)):(h(),t("span",ee,"未提供"))])])])]),s("div",null,[s("div",te,[w[14]||(w[14]=s("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"状态信息",-1)),s("div",se,[s("div",ae,[w[10]||(w[10]=s("span",{class:"text-sm text-gray-600"},"当前状态",-1)),s("span",{class:n([$(e.product.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(D(e.product.status)),3)]),s("div",re,[w[11]||(w[11]=s("span",{class:"text-sm text-gray-600"},"提交时间",-1)),s("span",le,o(T(e.product.created_at)),1)]),s("div",de,[w[12]||(w[12]=s("span",{class:"text-sm text-gray-600"},"更新时间",-1)),s("span",ce,o(T(e.product.updated_at)),1)]),s("div",oe,[w[13]||(w[13]=s("span",{class:"text-sm text-gray-600"},"提交用户",-1)),s("span",ne,o(e.product.submitted_by),1)])])]),"pending"===e.product.status?(h(),t("div",ie,[s("h4",ue,[a(r(u),{class:"w-4 h-4 mr-2"}),w[15]||(w[15]=i(" 待审核操作 "))]),s("div",me,[s("button",{onClick:Z,class:"w-full flex items-center justify-center px-4 py-2 bg-green-600 text-white rounded-md hover:bg-green-700 transition-colors"},[a(r(m),{class:"w-4 h-4 mr-2"}),w[16]||(w[16]=i(" 通过审核 "))]),s("button",{onClick:U,class:"w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"},[a(r(p),{class:"w-4 h-4 mr-2"}),w[17]||(w[17]=i(" 拒绝审核 "))])])])):d("",!0),s("div",pe,[s("button",{onClick:V,class:"w-full flex items-center justify-center px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"},[a(r(x),{class:"w-4 h-4 mr-2"}),w[18]||(w[18]=i(" 编辑产品 "))]),"approved"===e.product.status?(h(),t("button",{key:0,onClick:L,class:"w-full flex items-center justify-center px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"},[a(r(g),{class:"w-4 h-4 mr-2"}),w[19]||(w[19]=i(" 发布产品 "))])):d("",!0),"published"===e.product.status?(h(),t("button",{key:1,onClick:z,class:"w-full flex items-center justify-center px-4 py-2 bg-gray-600 text-white rounded-md hover:bg-gray-700 transition-colors"},[a(r(y),{class:"w-4 h-4 mr-2"}),w[20]||(w[20]=i(" 取消发布 "))])):d("",!0)]),s("div",xe,[w[22]||(w[22]=s("h4",{class:"text-sm font-medium text-red-800 mb-3"},"危险操作",-1)),s("button",{onClick:A,class:"w-full flex items-center justify-center px-4 py-2 bg-red-600 text-white rounded-md hover:bg-red-700 transition-colors"},[a(r(b),{class:"w-4 h-4 mr-2"}),w[21]||(w[21]=i(" 删除产品 "))])])])]),e.product.content?(h(),t("div",ge,[w[23]||(w[23]=s("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"详细介绍",-1)),s("div",ye,[s("p",be,o(e.product.content),1)])])):d("",!0),e.product.tags&&e.product.tags.length>0?(h(),t("div",ve,[w[24]||(w[24]=s("h4",{class:"text-sm font-medium text-gray-900 mb-3"},"产品标签",-1)),s("div",fe,[(h(!0),t(v,null,f(e.product.tags,e=>(h(),t("span",{key:e,class:"inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"},o(e),1))),128))])])):d("",!0)]),s("div",he,[s("button",{onClick:w[2]||(w[2]=t=>e.$emit("close")),class:"px-4 py-2 border border-gray-300 text-gray-700 rounded-md hover:bg-gray-50 transition-colors"}," 关闭 ")])])])]))}}),ke={class:"min-h-screen bg-gray-50"},_e={class:"bg-white shadow-sm"},je={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-6"},Ce={class:"flex justify-between items-center"},$e={class:"flex space-x-3"},De={class:"max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"},Te={class:"grid grid-cols-1 md:grid-cols-4 gap-6 mb-8"},Ze={class:"bg-white rounded-lg shadow p-6"},Ue={class:"flex items-center"},Ve={class:"flex-shrink-0"},Le={class:"ml-4"},ze={class:"text-2xl font-semibold text-gray-900"},Ae={class:"bg-white rounded-lg shadow p-6"},Ie={class:"flex items-center"},Me={class:"flex-shrink-0"},Ee={class:"ml-4"},Ne={class:"text-2xl font-semibold text-gray-900"},Se={class:"bg-white rounded-lg shadow p-6"},Pe={class:"flex items-center"},Re={class:"flex-shrink-0"},We={class:"ml-4"},qe={class:"text-2xl font-semibold text-gray-900"},Be={class:"bg-white rounded-lg shadow p-6"},Fe={class:"flex items-center"},Ge={class:"flex-shrink-0"},He={class:"ml-4"},Ke={class:"text-2xl font-semibold text-gray-900"},Je={class:"bg-white rounded-lg shadow mb-6"},Oe={class:"p-6 border-b border-gray-200"},Qe={class:"grid grid-cols-1 md:grid-cols-4 gap-4"},Xe={class:"relative"},Ye=["value"],et={class:"bg-white rounded-lg shadow"},tt={key:0,class:"p-8 text-center"},st={key:1,class:"p-8 text-center"},at={key:2,class:"overflow-x-auto"},rt={class:"min-w-full divide-y divide-gray-200"},lt={class:"bg-white divide-y divide-gray-200"},dt={class:"px-6 py-4"},ct={class:"flex items-center"},ot={class:"flex-shrink-0 w-12 h-12"},nt=["src","alt"],it={key:1,class:"w-12 h-12 rounded-lg bg-gray-200 flex items-center justify-center"},ut={class:"ml-4"},mt={class:"text-sm font-medium text-gray-900"},pt={class:"text-sm text-gray-500 max-w-xs truncate"},xt={class:"px-6 py-4"},gt={class:"text-sm text-gray-900"},yt={class:"text-sm text-gray-500"},bt={class:"px-6 py-4"},vt={class:"px-6 py-4 text-sm text-gray-500"},ft={class:"px-6 py-4"},ht={class:"flex space-x-2"},wt=["onClick"],kt=["onClick"],_t=["onClick"],jt=["onClick"],Ct=["onClick"],$t={key:3,class:"px-6 py-4 border-t border-gray-200"},Dt={class:"flex items-center justify-between"},Tt={class:"text-sm text-gray-700"},Zt={class:"flex space-x-2"},Ut=["disabled"],Vt={class:"px-3 py-1 text-sm text-gray-700"},Lt=["disabled"],zt=e({__name:"ProductManagementView",setup(e){const l=w(!1),x=w([]),g=w([]),y=w(null),b=w(1),M=w(10),E=k({search:"",status:"",category:"",sort:"created_at_desc"}),N=_(()=>({total:x.value.length,pending:x.value.filter(e=>"pending"===e.status).length,published:x.value.filter(e=>"published"===e.status).length,rejected:x.value.filter(e=>"rejected"===e.status).length})),S=(e,t)=>[...e].sort((e,s)=>{switch(t){case"created_at_desc":return new Date(s.created_at).getTime()-new Date(e.created_at).getTime();case"created_at_asc":return new Date(e.created_at).getTime()-new Date(s.created_at).getTime();case"name_asc":return e.name.localeCompare(s.name);case"name_desc":return s.name.localeCompare(e.name);case"price_asc":return e.price-s.price;case"price_desc":return s.price-e.price;default:return 0}}),P=_(()=>{const{search:e,status:t,category:s,sort:a}=E;if(!e&&!t&&!s)return S(x.value,a);const r=x.value.filter(a=>{if(e){const t=e.toLowerCase();if(!(a.name.toLowerCase().includes(t)||a.description.toLowerCase().includes(t)))return!1}return(!t||a.status===t)&&(!s||a.category_id===s)});return S(r,a)}),R=_(()=>{const e=(b.value-1)*M.value,t=e+M.value;return P.value.slice(e,t)}),W=_(()=>Math.ceil(P.value.length/M.value)),q=e=>{const t=g.value.find(t=>t.id===e);return(null==t?void 0:t.name)||"未知分类"},B=e=>{switch(e){case"pending":return"bg-yellow-100 text-yellow-800";case"approved":case"published":return"bg-green-100 text-green-800";case"rejected":return"bg-red-100 text-red-800";default:return"bg-gray-100 text-gray-800"}},F=e=>{switch(e){case"pending":return"待审核";case"approved":return"已通过";case"published":return"已发布";case"rejected":return"已拒绝";default:return"未知"}},G=async e=>{confirm(`确定要通过产品"${e.name}"吗？`)&&(e.status="approved",console.log("产品已通过:",e.name))},H=async e=>{const t=prompt(`请输入拒绝产品"${e.name}"的原因：`);t&&(e.status="rejected",console.log("产品已拒绝:",e.name,"原因:",t))},K=e=>{console.log("编辑产品:",e.name)},J=e=>{G(e),y.value=null},O=e=>{H(e),y.value=null},Q=e=>{K(e),y.value=null},X=async()=>{l.value=!0;try{console.log("数据已刷新")}finally{l.value=!1}};return j(()=>{g.value=[{id:"1",name:"开发工具"},{id:"2",name:"设计工具"},{id:"3",name:"办公软件"},{id:"4",name:"学习资源"},{id:"5",name:"其他工具"}],x.value=[{id:"1",name:"VS Code 插件包",description:"前端开发必备插件集合",price:0,category_id:"1",status:"pending",image_url:"",created_at:"2024-01-15T10:30:00Z",updated_at:"2024-01-15T10:30:00Z",submitted_by:"user1"},{id:"2",name:"UI 设计模板",description:"现代化的移动端UI设计模板",price:99.99,category_id:"2",status:"published",image_url:"",created_at:"2024-01-14T15:20:00Z",updated_at:"2024-01-14T15:20:00Z",submitted_by:"user2"},{id:"3",name:"Excel 自动化工具",description:"提高办公效率的Excel插件",price:49.99,category_id:"3",status:"rejected",image_url:"",created_at:"2024-01-13T09:15:00Z",updated_at:"2024-01-13T09:15:00Z",submitted_by:"user3"}],document.title="产品管理 - 管理后台"}),(e,w)=>{const k=Z("router-link");return h(),t("div",ke,[s("div",_e,[s("div",je,[s("div",Ce,[w[9]||(w[9]=s("div",null,[s("h1",{class:"text-2xl font-bold text-gray-900"},"产品管理"),s("p",{class:"mt-1 text-sm text-gray-600"}," 管理用户提交的产品，进行审核、编辑和发布 ")],-1)),s("div",$e,[s("button",{onClick:X,class:"inline-flex items-center px-4 py-2 border border-gray-300 rounded-md shadow-sm text-sm font-medium text-gray-700 bg-white hover:bg-gray-50"},[a(r($),{class:"w-4 h-4 mr-2"}),w[7]||(w[7]=i(" 刷新 "))]),a(k,{to:"/upload-product",class:"inline-flex items-center px-4 py-2 bg-blue-600 text-white rounded-md shadow-sm text-sm font-medium hover:bg-blue-700"},{default:D(()=>[a(r(T),{class:"w-4 h-4 mr-2"}),w[8]||(w[8]=i(" 添加产品 "))]),_:1,__:[8]})])])])]),s("div",De,[s("div",Te,[s("div",Ze,[s("div",Ue,[s("div",Ve,[a(r(c),{class:"w-8 h-8 text-blue-600"})]),s("div",Le,[w[10]||(w[10]=s("p",{class:"text-sm font-medium text-gray-600"},"总产品数",-1)),s("p",ze,o(N.value.total),1)])])]),s("div",Ae,[s("div",Ie,[s("div",Me,[a(r(u),{class:"w-8 h-8 text-yellow-600"})]),s("div",Ee,[w[11]||(w[11]=s("p",{class:"text-sm font-medium text-gray-600"},"待审核",-1)),s("p",Ne,o(N.value.pending),1)])])]),s("div",Se,[s("div",Pe,[s("div",Re,[a(r(m),{class:"w-8 h-8 text-green-600"})]),s("div",We,[w[12]||(w[12]=s("p",{class:"text-sm font-medium text-gray-600"},"已发布",-1)),s("p",qe,o(N.value.published),1)])])]),s("div",Be,[s("div",Fe,[s("div",Ge,[a(r(p),{class:"w-8 h-8 text-red-600"})]),s("div",He,[w[13]||(w[13]=s("p",{class:"text-sm font-medium text-gray-600"},"已拒绝",-1)),s("p",Ke,o(N.value.rejected),1)])])])]),s("div",Je,[s("div",Oe,[s("div",Qe,[s("div",null,[w[14]||(w[14]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"搜索产品",-1)),s("div",Xe,[a(r(V),{class:"absolute left-3 top-1/2 transform -translate-y-1/2 w-4 h-4 text-gray-400"}),U(s("input",{"onUpdate:modelValue":w[0]||(w[0]=e=>E.search=e),type:"text",placeholder:"产品名称或描述",class:"pl-10 w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},null,512),[[L,E.search]])])]),s("div",null,[w[16]||(w[16]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"状态",-1)),U(s("select",{"onUpdate:modelValue":w[1]||(w[1]=e=>E.status=e),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},w[15]||(w[15]=[A('<option value="">全部状态</option><option value="pending">待审核</option><option value="approved">已通过</option><option value="rejected">已拒绝</option><option value="published">已发布</option>',5)]),512),[[z,E.status]])]),s("div",null,[w[18]||(w[18]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"分类",-1)),U(s("select",{"onUpdate:modelValue":w[2]||(w[2]=e=>E.category=e),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[w[17]||(w[17]=s("option",{value:""},"全部分类",-1)),(h(!0),t(v,null,f(g.value,e=>(h(),t("option",{key:e.id,value:e.id},o(e.name),9,Ye))),128))],512),[[z,E.category]])]),s("div",null,[w[20]||(w[20]=s("label",{class:"block text-sm font-medium text-gray-700 mb-1"},"排序",-1)),U(s("select",{"onUpdate:modelValue":w[3]||(w[3]=e=>E.sort=e),class:"w-full px-3 py-2 border border-gray-300 rounded-md focus:ring-2 focus:ring-blue-500 focus:border-transparent"},w[19]||(w[19]=[A('<option value="created_at_desc">最新提交</option><option value="created_at_asc">最早提交</option><option value="name_asc">名称 A-Z</option><option value="name_desc">名称 Z-A</option><option value="price_asc">价格低到高</option><option value="price_desc">价格高到低</option>',6)]),512),[[z,E.sort]])])])])]),s("div",et,[w[24]||(w[24]=s("div",{class:"px-6 py-4 border-b border-gray-200"},[s("h3",{class:"text-lg font-medium text-gray-900"},"产品列表")],-1)),l.value?(h(),t("div",tt,[a(r(I),{class:"w-8 h-8 animate-spin mx-auto text-gray-400"}),w[21]||(w[21]=s("p",{class:"mt-2 text-gray-600"},"加载中...",-1))])):0===P.value.length?(h(),t("div",st,[a(r(c),{class:"w-12 h-12 mx-auto text-gray-400 mb-4"}),w[22]||(w[22]=s("p",{class:"text-gray-600"},"暂无产品数据",-1))])):(h(),t("div",at,[s("table",rt,[w[23]||(w[23]=s("thead",{class:"bg-gray-50"},[s("tr",null,[s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 产品信息 "),s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 分类/价格 "),s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 状态 "),s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 提交时间 "),s("th",{class:"px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider"}," 操作 ")])],-1)),s("tbody",lt,[(h(!0),t(v,null,f(R.value,e=>{return h(),t("tr",{key:e.id,class:"hover:bg-gray-50"},[s("td",dt,[s("div",ct,[s("div",ot,[e.image_url?(h(),t("img",{key:0,src:e.image_url,alt:e.name,class:"w-12 h-12 rounded-lg object-cover"},null,8,nt)):(h(),t("div",it,[a(r(c),{class:"w-6 h-6 text-gray-400"})]))]),s("div",ut,[s("div",mt,o(e.name),1),s("div",pt,o(e.description),1)])])]),s("td",xt,[s("div",gt,o(q(e.category_id)),1),s("div",yt,o(0===e.price?"免费":`¥${e.price}`),1)]),s("td",bt,[s("span",{class:n([B(e.status),"inline-flex px-2 py-1 text-xs font-semibold rounded-full"])},o(F(e.status)),3)]),s("td",vt,o((l=e.created_at,new Date(l).toLocaleDateString("zh-CN",{year:"numeric",month:"2-digit",day:"2-digit",hour:"2-digit",minute:"2-digit"}))),1),s("td",ft,[s("div",ht,[s("button",{onClick:t=>(e=>{y.value=e})(e),class:"text-blue-600 hover:text-blue-800 text-sm font-medium"}," 查看 ",8,wt),"pending"===e.status?(h(),t("button",{key:0,onClick:t=>G(e),class:"text-green-600 hover:text-green-800 text-sm font-medium"}," 通过 ",8,kt)):d("",!0),"pending"===e.status?(h(),t("button",{key:1,onClick:t=>H(e),class:"text-red-600 hover:text-red-800 text-sm font-medium"}," 拒绝 ",8,_t)):d("",!0),s("button",{onClick:t=>K(e),class:"text-gray-600 hover:text-gray-800 text-sm font-medium"}," 编辑 ",8,jt),s("button",{onClick:t=>(async e=>{if(confirm(`确定要删除产品"${e.name}"吗？此操作不可恢复。`)){const t=x.value.findIndex(t=>t.id===e.id);t>-1&&x.value.splice(t,1),console.log("产品已删除:",e.name)}})(e),class:"text-red-600 hover:text-red-800 text-sm font-medium"}," 删除 ",8,Ct)])])]);var l}),128))])])])),W.value>1?(h(),t("div",$t,[s("div",Dt,[s("div",Tt," 显示 "+o((b.value-1)*M.value+1)+" 到 "+o(Math.min(b.value*M.value,P.value.length))+" 条， 共 "+o(P.value.length)+" 条记录 ",1),s("div",Zt,[s("button",{disabled:1===b.value,onClick:w[4]||(w[4]=e=>b.value--),class:"px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"}," 上一页 ",8,Ut),s("span",Vt,o(b.value)+" / "+o(W.value),1),s("button",{disabled:b.value===W.value,onClick:w[5]||(w[5]=e=>b.value++),class:"px-3 py-1 border border-gray-300 rounded text-sm disabled:opacity-50 disabled:cursor-not-allowed hover:bg-gray-50"}," 下一页 ",8,Lt)])])])):d("",!0)])]),y.value?(h(),C(we,{key:0,product:y.value,onClose:w[6]||(w[6]=e=>y.value=null),onApprove:J,onReject:O,onEdit:Q},null,8,["product"])):d("",!0)])}}});export{zt as default};
