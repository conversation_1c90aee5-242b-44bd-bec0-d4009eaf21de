import{s as t}from"./index-DdsLZGoA.js";import"./vendor-VsOxy-e0.js";class r{static async createOrder(r,e){try{const{data:d,error:i}=await t.from("products").select("id, name, price, is_digital").eq("id",r.productId).eq("status","active").single();if(i||!d)throw new Error("产品不存在或已下架");const a=d.price*r.quantity,{data:o,error:s}=await t.from("orders").insert({user_id:e,total_amount:a,currency:"CNY",status:"pending",billing_address:r.billingAddress}).select().single();if(s)throw s;const{data:n,error:c}=await t.from("order_items").insert({order_id:o.id,product_id:r.productId,quantity:r.quantity,unit_price:d.price,total_price:a}).select().single();if(c)throw c;return{id:o.id,userId:o.user_id,items:[{id:n.id,orderId:o.id,productId:r.productId,quantity:r.quantity,unitPrice:d.price,totalPrice:a,createdAt:n.created_at,product:{id:d.id,name:d.name,shortDescription:"",images:[]}}],totalAmount:a,currency:o.currency,status:o.status,billingAddress:r.billingAddress,createdAt:o.created_at,updatedAt:o.updated_at}}catch(d){throw console.error("创建订单失败:",d),new Error("创建订单失败")}}static async processPayment(r){try{const{error:e}=await t.from("orders").update({status:"paid",payment_method:r.paymentMethod,payment_id:r.paymentId,completed_at:(new Date).toISOString(),updated_at:(new Date).toISOString()}).eq("id",r.orderId).eq("status","pending");if(e)throw e}catch(e){throw console.error("处理支付失败:",e),new Error("处理支付失败")}}static async verifyDownloadPermission(r,e){try{const{data:d,error:i}=await t.from("order_items").select("\n          id,\n          orders!inner(\n            id,\n            user_id,\n            status\n          )\n        ").eq("product_id",r).eq("orders.user_id",e).eq("orders.status","paid");if(i)throw i;return d&&d.length>0}catch(d){return console.error("验证下载权限失败:",d),!1}}static async getUserOrders(r){try{const{data:e,error:d}=await t.from("orders").select("\n          *,\n          order_items(\n            *,\n            products(id, name, images, short_description)\n          )\n        ").eq("user_id",r).order("created_at",{ascending:!1});if(d)throw d;return e.map(t=>({id:t.id,userId:t.user_id,items:t.order_items.map(t=>({id:t.id,orderId:t.order_id,productId:t.product_id,quantity:t.quantity,unitPrice:t.unit_price,totalPrice:t.total_price,createdAt:t.created_at,product:t.products?{id:t.products.id,name:t.products.name,shortDescription:t.products.short_description||"",images:t.products.images||[]}:void 0})),totalAmount:t.total_amount,currency:t.currency,status:t.status,paymentMethod:t.payment_method,paymentId:t.payment_id,billingAddress:t.billing_address,createdAt:t.created_at,updatedAt:t.updated_at,completedAt:t.completed_at}))}catch(e){throw console.error("获取用户订单失败:",e),new Error("获取用户订单失败")}}static async cancelOrder(r,e){try{const{error:d}=await t.from("orders").update({status:"cancelled",updated_at:(new Date).toISOString()}).eq("id",r).eq("user_id",e).eq("status","pending");if(d)throw d}catch(d){throw console.error("取消订单失败:",d),new Error("取消订单失败")}}static async getOrderById(r,e){try{const{data:d,error:i}=await t.from("orders").select("\n          *,\n          order_items(\n            *,\n            products(id, name, images, short_description, download_url)\n          )\n        ").eq("id",r).eq("user_id",e).single();if(i)throw i;return d?{id:d.id,userId:d.user_id,items:d.order_items.map(t=>({id:t.id,orderId:t.order_id,productId:t.product_id,quantity:t.quantity,unitPrice:t.unit_price,totalPrice:t.total_price,createdAt:t.created_at,product:t.products?{id:t.products.id,name:t.products.name,shortDescription:t.products.short_description||"",images:t.products.images||[],downloadUrl:t.products.download_url}:void 0})),totalAmount:d.total_amount,currency:d.currency,status:d.status,paymentMethod:d.payment_method,paymentId:d.payment_id,billingAddress:d.billing_address,createdAt:d.created_at,updatedAt:d.updated_at,completedAt:d.completed_at}:null}catch(d){return console.error("获取订单详情失败:",d),null}}}export{r as OrderService};
