import{d as a,r as e,Z as l,m as s,q as t,x as u,t as n,A as o,B as r,C as i,N as v,y as c,a1 as d,a0 as m,G as f}from"./vendor-VsOxy-e0.js";import{_ as p}from"./index-DdsLZGoA.js";const b={class:"forgot-password-view"},g={class:"form-group"},y=["disabled"],_=["disabled"],h={key:0,class:"loading-spinner"},k={key:0,class:"error-message"},w={key:1,class:"success-state"},x={class:"success-actions"},I=["disabled"],P={class:"forgot-footer"},j=p(a({__name:"ForgotPasswordView",setup(a){const p=e(!1),j=e(null),q=e(""),C=e(!1),E=e(0);let T=null;const V=async()=>{try{p.value=!0,j.value=null,await new Promise(a=>setTimeout(a,1e3)),C.value=!0,B()}catch(a){j.value=a instanceof Error?a.message:"发送失败，请重试"}finally{p.value=!1}},A=async()=>{if(!(E.value>0))try{p.value=!0,j.value=null,await new Promise(a=>setTimeout(a,500)),B()}catch(a){j.value=a instanceof Error?a.message:"重新发送失败，请重试"}finally{p.value=!1}},B=()=>{E.value=60,T=setInterval(()=>{E.value--,E.value<=0&&(clearInterval(T),T=null)},1e3)};return l(()=>{T&&clearInterval(T)}),(a,e)=>{const l=m("router-link");return f(),s("div",b,[e[10]||(e[10]=t("div",{class:"forgot-header"},[t("h1",null,"忘记密码"),t("p",null,"输入您的邮箱地址，我们将发送重置密码的链接")],-1)),C.value?(f(),s("div",w,[e[5]||(e[5]=t("div",{class:"success-icon"},"✅",-1)),e[6]||(e[6]=t("h3",null,"邮件已发送",-1)),t("p",null,[e[2]||(e[2]=v(" 我们已向 ")),t("strong",null,i(q.value),1),e[3]||(e[3]=v(" 发送了重置密码的链接 "))]),e[7]||(e[7]=t("p",{class:"help-text"}," 请检查您的邮箱（包括垃圾邮件文件夹），并点击链接重置密码 ",-1)),t("div",x,[t("button",{class:"resend-btn",disabled:E.value>0,onClick:A},i(E.value>0?`${E.value}秒后可重发`:"重新发送"),9,I),c(l,{to:"/auth/login",class:"back-btn"},{default:d(()=>e[4]||(e[4]=[v("返回登录")])),_:1,__:[4]})])])):(f(),s("form",{key:0,class:"forgot-form",onSubmit:u(V,["prevent"])},[t("div",g,[e[1]||(e[1]=t("label",{for:"email"},"邮箱地址",-1)),o(t("input",{id:"email","onUpdate:modelValue":e[0]||(e[0]=a=>q.value=a),type:"email",required:"",placeholder:"请输入您的邮箱地址",disabled:p.value},null,8,y),[[r,q.value]])]),t("button",{type:"submit",class:"submit-btn",disabled:p.value||!q.value},[p.value?(f(),s("div",h)):n("",!0),t("span",null,i(p.value?"发送中...":"发送重置链接"),1)],8,_),j.value?(f(),s("div",k,i(j.value),1)):n("",!0)],32)),t("div",P,[t("p",null,[e[9]||(e[9]=v(" 记起密码了？ ")),c(l,{to:"/auth/login",class:"login-link"},{default:d(()=>e[8]||(e[8]=[v("立即登录")])),_:1,__:[8]})])])])}}}),[["__scopeId","data-v-5fa570f1"]]);export{j as default};
