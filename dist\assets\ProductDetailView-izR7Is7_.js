const __vite__mapDeps=(i,m=__vite__mapDeps,d=(m.f||(m.f=["./index-DdsLZGoA.js","./vendor-VsOxy-e0.js","./index-BukxQyzs.css"])))=>i.map(i=>d[i]);
import{d as a,c as s,f as e,_ as t}from"./index-DdsLZGoA.js";import{d as i,r as l,o as c,m as o,q as n,y as r,N as u,u as d,aR as p,C as v,t as m,F as g,z as h,Q as f,aQ as y,H as b,ar as k,G as _}from"./vendor-VsOxy-e0.js";const w={class:"product-detail-view"},C={class:"container"},P={class:"back-section"},A={key:0,class:"product-detail"},I={class:"product-gallery"},j={class:"main-image"},q=["src","alt"],x={class:"product-info"},S={class:"product-header"},D={class:"product-title"},E={class:"product-price"},L={class:"current-price"},Q={key:0,class:"original-price"},R={class:"product-description"},T={class:"product-tags"},U={class:"tags-list"},V={class:"product-actions"},z={key:1,class:"loading-state"},F={key:2,class:"error-state"},G=t(i({__name:"ProductDetailView",setup(t){const i=a(),G=s(),H=l(null),M=l(!0),N=l(!1),O=[{id:1,name:"高效办公套件",description:"提升办公效率的完整解决方案，包含文档处理、项目管理、时间管理、团队协作等多个模块。支持多平台同步，让您随时随地高效办公。",price:299,originalPrice:399,image:"https://images.unsplash.com/photo-1551288049-bebda4e38f71?w=800&h=600&fit=crop",category:"office",tags:["办公","效率","文档","项目管理","团队协作"]},{id:2,name:"设计师工具包",description:"专业设计师必备工具集合，包含UI设计、图标制作、原型设计、色彩搭配等功能。提供丰富的设计素材和模板，助力创意实现。",price:199,image:"https://images.unsplash.com/photo-1558655146-9f40138edfeb?w=800&h=600&fit=crop",category:"design",tags:["设计","UI","图标","创意","原型"]},{id:3,name:"开发者助手",description:"程序员开发必备工具，代码编辑、调试、部署一站式解决。支持多种编程语言，集成版本控制，提供智能代码补全和错误检测。",price:399,originalPrice:499,image:"https://images.unsplash.com/photo-1461749280684-dccba630e2f6?w=800&h=600&fit=crop",category:"development",tags:["开发","编程","调试","部署","版本控制"]}],$=()=>{window.history.length>1?G.go(-1):G.push("/products")},B=async()=>{if(H.value)try{console.log("购买产品:",H.value.name);const{useAuthStore:a}=await e(async()=>{const{useAuthStore:a}=await import("./index-DdsLZGoA.js").then(a=>a.g);return{useAuthStore:a}},__vite__mapDeps([0,1,2]),import.meta.url);if(!a().isAuthenticated)return void G.push({name:"Login",query:{redirect:`/product/${H.value.id}`}});G.push({path:"/payment",query:{product:H.value.id}})}catch(a){console.error("购买流程错误:",a),alert("购买失败，请稍后重试")}},J=()=>{H.value&&console.log("预览产品:",H.value.name)},K=()=>{N.value=!N.value,console.log(N.value?"已添加到收藏":"已取消收藏")};return c(()=>{(()=>{const a=parseInt(i.params.id);setTimeout(()=>{const s=O.find(s=>s.id===a);s&&(H.value=s,N.value=Math.random()>.5),M.value=!1},300)})()}),(a,s)=>(_(),o("div",w,[n("div",C,[n("div",P,[n("button",{class:"back-btn",onClick:$},[r(d(p),{class:"icon"}),s[0]||(s[0]=u(" 返回产品列表 "))])]),H.value?(_(),o("div",A,[n("div",I,[n("div",j,[n("img",{src:H.value.image,alt:H.value.name},null,8,q)])]),n("div",x,[n("div",S,[n("h1",D,v(H.value.name),1),n("div",E,[n("span",L,"¥"+v(H.value.price),1),H.value.originalPrice?(_(),o("span",Q," ¥"+v(H.value.originalPrice),1)):m("",!0)])]),n("div",R,[s[1]||(s[1]=n("h3",null,"产品描述",-1)),n("p",null,v(H.value.description),1)]),n("div",T,[s[2]||(s[2]=n("h3",null,"标签",-1)),n("div",U,[(_(!0),o(g,null,h(H.value.tags,a=>(_(),o("span",{key:a,class:"tag"},v(a),1))),128))])]),n("div",V,[n("button",{class:"buy-btn",onClick:B},[r(d(f),{class:"icon"}),s[3]||(s[3]=u(" 立即购买 "))]),n("button",{class:"demo-btn",onClick:J},[r(d(y),{class:"icon"}),s[4]||(s[4]=u(" 在线预览 "))]),n("button",{class:"favorite-btn",onClick:K},[r(d(k),{class:b(["icon",{filled:N.value}])},null,8,["class"]),u(" "+v(N.value?"已收藏":"收藏"),1)])])])])):M.value?(_(),o("div",z,s[5]||(s[5]=[n("div",{class:"loading-spinner"},null,-1),n("p",null,"加载中...",-1)]))):(_(),o("div",F,[s[6]||(s[6]=n("div",{class:"error-icon"},"❌",-1)),s[7]||(s[7]=n("h3",null,"产品未找到",-1)),s[8]||(s[8]=n("p",null,"抱歉，您访问的产品不存在或已下架",-1)),n("button",{class:"error-action",onClick:$},"返回产品列表")]))])]))}}),[["__scopeId","data-v-84e53f2a"]]);export{G as default};
