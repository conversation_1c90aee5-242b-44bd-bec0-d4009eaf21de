var e=Object.defineProperty,a=(a,t,s)=>((a,t,s)=>t in a?e(a,t,{enumerable:!0,configurable:!0,writable:!0,value:s}):a[t]=s)(a,"symbol"!=typeof t?t+"":t,s);import{d as t,r as s,c as l,o,n as r,w as n,m as c,H as i,q as u,t as d,y as v,A as p,u as g,a2 as h,B as m,D as y,X as f,as as k,F as w,z as b,v as _,I as S,C as x,af as T,a8 as C,N as q,K as $,at as M,R as O,au as H,av as E,G as R}from"./vendor-VsOxy-e0.js";import{s as A,T as D,a as B,c as I,e as P,_ as U}from"./index-DdsLZGoA.js";const F=new class{constructor(){a(this,"searchHistory",[]),a(this,"popularQueries",new Map)}async search(e){const a=Date.now(),{query:t,type:s="all",limit:l=20,offset:o=0}=e;try{let l=[],o=0,r={categories:[],tags:[],priceRanges:[]};switch(this.addToHistory(t,s),s){case"tools":const a=await this.searchTools(e);l=a.items,o=a.total,r=a.facets;break;case"products":const t=await this.searchProducts(e);l=t.items,o=t.total,r=t.facets;break;case"categories":const s=await this.searchCategories(e);l=s.items,o=s.total;break;default:const n=await this.searchAll(e);l=n.items,o=n.total,r=n.facets}const n=await this.generateSuggestions(t,s);return{items:l,total:o,query:t,suggestions:n,facets:r,searchTime:Date.now()-a}}catch(r){throw console.error("搜索失败:",r),r}}async searchTools(e){const{query:a,category:t,tags:s,sortBy:l="relevance",sortOrder:o="desc",limit:r=20,offset:n=0}=e;let c=A.from(D.TOOLS).select("\n        *,\n        categories!inner(name, icon, color),\n        tool_tags!inner(tags!inner(name, color))\n      ",{count:"exact"});a&&(c=c.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%,\n        meta_title.ilike.%${a}%,\n        meta_description.ilike.%${a}%\n      `)),t&&(c=c.eq("category_id",t)),s&&s.length>0&&(c=c.in("tool_tags.tag_id",s)),c=c.eq("status","active"),"relevance"===l&&a?(c=c.order("is_featured",{ascending:!1}),c=c.order("click_count",{ascending:!1})):c=c.order(l,{ascending:"asc"===o}),c=c.range(n,n+r-1);const{data:i,error:u,count:d}=await c;if(u)throw u;return{items:i||[],total:d||0,facets:await this.generateToolsFacets(a,t,s)}}async searchProducts(e){const{query:a,category:t,priceRange:s,sortBy:l="relevance",sortOrder:o="desc",limit:r=20,offset:n=0}=e;let c=A.from(D.PRODUCTS).select("\n        *,\n        product_categories!inner(name, icon, color)\n      ",{count:"exact"});a&&(c=c.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%,\n        short_description.ilike.%${a}%,\n        meta_title.ilike.%${a}%,\n        meta_description.ilike.%${a}%\n      `)),t&&(c=c.eq("category_id",t)),s&&(c=c.gte("price",s[0]).lte("price",s[1])),c=c.eq("status","active"),c="relevance"===l&&a?c.order("is_featured",{ascending:!1}):c.order(l,{ascending:"asc"===o}),c=c.range(n,n+r-1);const{data:i,error:u,count:d}=await c;if(u)throw u;return{items:i||[],total:d||0,facets:await this.generateProductsFacets(a,t,s)}}async searchCategories(e){const{query:a,limit:t=20,offset:s=0}=e;let l=A.from(D.CATEGORIES).select("*",{count:"exact"});a&&(l=l.or(`\n        name.ilike.%${a}%,\n        description.ilike.%${a}%\n      `)),l=l.eq("is_active",!0).order("sort_order",{ascending:!0}).range(s,s+t-1);const{data:o,error:r,count:n}=await l;if(r)throw r;return{items:o||[],total:n||0}}async searchAll(e){const{limit:a=20}=e,[t,s,l]=await Promise.all([this.searchTools({...e,limit:Math.ceil(a/3)}),this.searchProducts({...e,limit:Math.ceil(a/3)}),this.searchCategories({...e,limit:Math.ceil(a/3)})]);return{items:[...t.items.map(e=>({...e,_type:"tool"})),...s.items.map(e=>({...e,_type:"product"})),...l.items.map(e=>({...e,_type:"category"}))],total:t.total+s.total+l.total,facets:{categories:[...t.facets.categories,...s.facets.categories],tags:t.facets.tags,priceRanges:s.facets.priceRanges}}}async generateToolsFacets(e,a,t){const s=A.from(D.CATEGORIES).select("\n        id, name,\n        tools!inner(id)\n      ",{count:"exact"}).eq("is_active",!0);e&&s.or(`\n        tools.name.ilike.%${e}%,\n        tools.description.ilike.%${e}%\n      `);const{data:l}=await s,o=(l||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.tools)?void 0:a.length)||0}}),r=A.from(D.TAGS).select("\n        id, name,\n        tool_tags!inner(tools!inner(id))\n      ",{count:"exact"});e&&r.or(`\n        tool_tags.tools.name.ilike.%${e}%,\n        tool_tags.tools.description.ilike.%${e}%\n      `);const{data:n}=await r;return{categories:o,tags:(n||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.tool_tags)?void 0:a.length)||0}}),priceRanges:[]}}async generateProductsFacets(e,a,t){const s=A.from(D.PRODUCT_CATEGORIES).select("\n        id, name,\n        products!inner(id)\n      ",{count:"exact"});e&&s.or(`\n        products.name.ilike.%${e}%,\n        products.description.ilike.%${e}%\n      `);const{data:l}=await s;return{categories:(l||[]).map(e=>{var a;return{name:e.name,count:(null==(a=e.products)?void 0:a.length)||0}}),tags:[],priceRanges:[{range:"0-50",count:0},{range:"50-100",count:0},{range:"100-500",count:0},{range:"500+",count:0}]}}async generateSuggestions(e,a){if(!e||e.length<2)return[];const t=[];try{const{data:a}=await A.from(D.TOOLS).select("name").ilike("name",`%${e}%`).eq("status","active").limit(5);null==a||a.forEach(e=>{t.includes(e.name)||t.push(e.name)});const{data:s}=await A.from(D.PRODUCTS).select("name").ilike("name",`%${e}%`).eq("status","active").limit(5);null==s||s.forEach(e=>{t.includes(e.name)||t.push(e.name)});const{data:l}=await A.from(D.CATEGORIES).select("name").ilike("name",`%${e}%`).eq("is_active",!0).limit(3);null==l||l.forEach(e=>{t.includes(e.name)||t.push(e.name)})}catch(s){console.error("生成搜索建议失败:",s)}return t.slice(0,8)}async getPopularSearches(e=10){return Array.from(this.popularQueries.entries()).sort((e,a)=>a[1]-e[1]).slice(0,e).map(([e])=>e)}getSearchHistory(e=10){return this.searchHistory.sort((e,a)=>a.timestamp.getTime()-e.timestamp.getTime()).slice(0,e)}clearSearchHistory(){this.searchHistory=[],localStorage.removeItem("search_history")}saveHistoryToStorage(){try{localStorage.setItem("search_history",JSON.stringify(this.searchHistory))}catch(e){console.error("保存搜索历史失败:",e)}}loadHistoryFromStorage(){try{const e=localStorage.getItem("search_history");if(e){const a=JSON.parse(e);this.searchHistory=a.map(e=>({...e,timestamp:new Date(e.timestamp)}))}}catch(e){console.error("加载搜索历史失败:",e),this.searchHistory=[]}}addToHistory(e,a){const t={id:Date.now().toString(),query:e,type:a,timestamp:new Date,results_count:0};this.searchHistory=this.searchHistory.filter(t=>t.query!==e||t.type!==a),this.searchHistory.unshift(t),this.searchHistory.length>50&&(this.searchHistory=this.searchHistory.slice(0,50));const s=this.popularQueries.get(e)||0;this.popularQueries.set(e,s+1)}async getSmartSuggestions(e){const a=[];if(!e||e.length<2){return(await this.getPopularSearches(5)).map(e=>({text:e,type:"query"}))}try{(await this.generateSuggestions(e,"all")).forEach(e=>{a.push({text:e,type:"query"})});const{data:t}=await A.from(D.CATEGORIES).select("name").ilike("name",`%${e}%`).eq("is_active",!0).limit(3);null==t||t.forEach(e=>{a.push({text:e.name,type:"category"})});const{data:s}=await A.from(D.TAGS).select("name").ilike("name",`%${e}%`).limit(3);null==s||s.forEach(e=>{a.push({text:e.name,type:"tag"})})}catch(t){console.error("获取智能建议失败:",t)}return a.slice(0,10)}},G={class:"search-input-container"},V={class:"search-input-wrapper"},Q=["placeholder"],j={class:"search-actions"},N={key:0,class:"suggestions-dropdown"},J={key:0,class:"suggestions-section"},K=["onClick","onMouseenter"],L={class:"suggestion-text"},z={class:"suggestion-type"},W={key:1,class:"suggestions-section"},X=["onClick","onMouseenter"],Y={class:"suggestion-text"},Z={class:"suggestion-meta"},ee={key:2,class:"suggestions-section"},ae={class:"popular-searches"},te=["onClick"],se={key:0,class:"advanced-search-panel"},le={class:"advanced-search-content"},oe={class:"advanced-row"},re={class:"advanced-group"},ne=["value"],ce={class:"advanced-group"},ie={class:"advanced-group"},ue={key:0,class:"advanced-row"},de={class:"advanced-group"},ve={class:"price-range"},pe={class:"advanced-row"},ge={class:"advanced-group full-width"},he={class:"tags-input"},me={class:"selected-tags"},ye=["onClick"],fe={key:0,class:"tags-dropdown"},ke=["onClick"],we={key:1,class:"search-status"},be={key:2,class:"search-stats"},_e=U(t({__name:"EnhancedSearchBox",props:{placeholder:{default:"搜索工具、产品、分类..."},autoFocus:{type:Boolean,default:!1},showAdvanced:{type:Boolean,default:!1},defaultType:{default:"all"}},emits:["search","clear","focus","blur"],setup(e,{expose:a,emit:t}){const A=e,D=t,U=I(),_e=B(),Se=s(),xe=s(""),Te=s(A.defaultType),Ce=s(!1),qe=s(!1),$e=s(!1),Me=s(A.showAdvanced),Oe=s(!1),He=s(-1),Ee=s([]),Re=s([]),Ae=s([]),De=s({category:"",sortBy:"relevance",sortOrder:"desc",priceMin:null,priceMax:null}),Be=s([]),Ie=s(""),Pe=s([]),Ue=s(null),Fe=l(()=>_e.categories),Ge=l(()=>[...Ee.value,...Re.value.map(e=>({text:e.query,type:"query"}))]),Ve=()=>{Ce.value=!0,qe.value=!0,$e.value=!0,D("focus"),Je()},Qe=()=>{setTimeout(()=>{Ce.value=!1,$e.value=!1,He.value=-1},200),D("blur")},je=()=>{He.value=-1,Ke()},Ne=e=>{var a;switch(e.key){case"ArrowDown":e.preventDefault(),He.value=Math.min(He.value+1,Ge.value.length-1);break;case"ArrowUp":e.preventDefault(),He.value=Math.max(He.value-1,-1);break;case"Enter":e.preventDefault(),He.value>=0?Le(Ge.value[He.value].text):ze();break;case"Escape":null==(a=Se.value)||a.blur()}},Je=async()=>{try{xe.value?Ee.value=await F.getSmartSuggestions(xe.value):(Ee.value=[],Re.value=F.getSearchHistory(5),Ae.value=await F.getPopularSearches(8))}catch(e){console.error("加载搜索建议失败:",e)}},Ke=P(Je,300),Le=e=>{xe.value=e,$e.value=!1,ze()},ze=async()=>{if(xe.value.trim())try{Oe.value=!0;const e={query:xe.value.trim(),type:Te.value,category:De.value.category||void 0,tags:Be.value.map(e=>e.id),priceRange:De.value.priceMin&&De.value.priceMax?[De.value.priceMin,De.value.priceMax]:void 0,sortBy:De.value.sortBy,sortOrder:De.value.sortOrder,limit:20},a=await F.search(e);Ue.value=a,D("search",a),U.push({name:"SearchResults",query:{q:xe.value,type:Te.value,...De.value}})}catch(e){console.error("搜索失败:",e)}finally{Oe.value=!1}},We=()=>{var e;xe.value="",Ue.value=null,D("clear"),null==(e=Se.value)||e.focus()},Xe=()=>{F.clearSearchHistory(),Re.value=[]},Ye=()=>{Me.value=!Me.value,qe.value=Me.value},Ze=()=>{De.value={category:"",sortBy:"relevance",sortOrder:"desc",priceMin:null,priceMax:null},Be.value=[]},ea=()=>{xe.value&&ze()},aa=async()=>{Ie.value||(Pe.value=[])},ta=e=>{switch(e){case"category":return E;case"tag":return H;case"tool":case"product":return O;default:return M}},sa=e=>{switch(e){case"category":return"分类";case"tag":return"标签";case"tool":return"工具";case"product":return"产品";default:return"搜索"}},la=e=>{const a=(new Date).getTime()-e.getTime(),t=Math.floor(a/6e4),s=Math.floor(a/36e5),l=Math.floor(a/864e5);return t<1?"刚刚":t<60?`${t}分钟前`:s<24?`${s}小时前`:`${l}天前`};return o(async()=>{var e;A.autoFocus&&(await r(),null==(e=Se.value)||e.focus()),0===_e.categories.length&&await _e.loadCategories()}),n(xe,()=>{xe.value||(Ue.value=null)}),a({focus:()=>{var e;return null==(e=Se.value)?void 0:e.focus()},clear:We,search:ze}),(e,a)=>(R(),c("div",{class:i(["enhanced-search-box",{"is-focused":Ce.value,"is-expanded":qe.value}])},[u("div",G,[u("div",V,[v(g(h),{class:"search-icon"}),p(u("input",{ref_key:"searchInput",ref:Se,"onUpdate:modelValue":a[0]||(a[0]=e=>xe.value=e),type:"text",class:"search-input",placeholder:e.placeholder,onFocus:Ve,onBlur:Qe,onKeydown:Ne,onInput:je},null,40,Q),[[m,xe.value]]),u("div",j,[p(u("select",{"onUpdate:modelValue":a[1]||(a[1]=e=>Te.value=e),class:"search-type-select"},a[8]||(a[8]=[u("option",{value:"all"},"全部",-1),u("option",{value:"tools"},"工具",-1),u("option",{value:"products"},"产品",-1),u("option",{value:"categories"},"分类",-1)]),512),[[y,Te.value]]),xe.value?(R(),c("button",{key:0,onClick:We,class:"clear-button",type:"button"},[v(g(f),{class:"icon"})])):d("",!0),u("button",{onClick:Ye,class:i(["advanced-button",{active:Me.value}]),type:"button"},[v(g(k),{class:"icon"})],2)])]),$e.value&&(Ee.value.length>0||Re.value.length>0)?(R(),c("div",N,[Ee.value.length>0?(R(),c("div",J,[a[9]||(a[9]=u("div",{class:"suggestions-header"},"搜索建议",-1)),(R(!0),c(w,null,b(Ee.value,(e,a)=>(R(),c("div",{key:`suggestion-${a}`,class:i(["suggestion-item",{active:He.value===a}]),onClick:a=>Le(e.text),onMouseenter:e=>He.value=a},[(R(),_(S(ta(e.type)),{class:"suggestion-icon"})),u("span",L,x(e.text),1),u("span",z,x(sa(e.type)),1)],42,K))),128))])):d("",!0),Re.value.length>0&&!xe.value?(R(),c("div",W,[u("div",{class:"suggestions-header"},[a[10]||(a[10]=u("span",null,"最近搜索",-1)),u("button",{onClick:Xe,class:"clear-history-button"}," 清除 ")]),(R(!0),c(w,null,b(Re.value,(e,a)=>(R(),c("div",{key:`history-${e.id}`,class:i(["suggestion-item history-item",{active:He.value===Ee.value.length+a}]),onClick:a=>Le(e.query),onMouseenter:e=>He.value=Ee.value.length+a},[v(g(T),{class:"suggestion-icon"}),u("span",Y,x(e.query),1),u("span",Z,x(la(e.timestamp)),1)],42,X))),128))])):d("",!0),Ae.value.length>0&&!xe.value?(R(),c("div",ee,[a[11]||(a[11]=u("div",{class:"suggestions-header"},"热门搜索",-1)),u("div",ae,[(R(!0),c(w,null,b(Ae.value,e=>(R(),c("button",{key:e,onClick:a=>Le(e),class:"popular-search-tag"},x(e),9,te))),128))])])):d("",!0)])):d("",!0)]),Me.value?(R(),c("div",se,[u("div",le,[u("div",oe,[u("div",re,[a[13]||(a[13]=u("label",{class:"advanced-label"},"分类",-1)),p(u("select",{"onUpdate:modelValue":a[2]||(a[2]=e=>De.value.category=e),class:"advanced-select"},[a[12]||(a[12]=u("option",{value:""},"所有分类",-1)),(R(!0),c(w,null,b(Fe.value,e=>(R(),c("option",{key:e.id,value:e.id},x(e.name),9,ne))),128))],512),[[y,De.value.category]])]),u("div",ce,[a[15]||(a[15]=u("label",{class:"advanced-label"},"排序",-1)),p(u("select",{"onUpdate:modelValue":a[3]||(a[3]=e=>De.value.sortBy=e),class:"advanced-select"},a[14]||(a[14]=[C('<option value="relevance" data-v-aa9aa83a>相关性</option><option value="name" data-v-aa9aa83a>名称</option><option value="created_at" data-v-aa9aa83a>创建时间</option><option value="click_count" data-v-aa9aa83a>热度</option><option value="price" data-v-aa9aa83a>价格</option>',5)]),512),[[y,De.value.sortBy]])]),u("div",ie,[a[17]||(a[17]=u("label",{class:"advanced-label"},"顺序",-1)),p(u("select",{"onUpdate:modelValue":a[4]||(a[4]=e=>De.value.sortOrder=e),class:"advanced-select"},a[16]||(a[16]=[u("option",{value:"desc"},"降序",-1),u("option",{value:"asc"},"升序",-1)]),512),[[y,De.value.sortOrder]])])]),"products"===Te.value?(R(),c("div",ue,[u("div",de,[a[19]||(a[19]=u("label",{class:"advanced-label"},"价格范围",-1)),u("div",ve,[p(u("input",{"onUpdate:modelValue":a[5]||(a[5]=e=>De.value.priceMin=e),type:"number",placeholder:"最低价",class:"price-input",min:"0"},null,512),[[m,De.value.priceMin,void 0,{number:!0}]]),a[18]||(a[18]=u("span",{class:"price-separator"},"-",-1)),p(u("input",{"onUpdate:modelValue":a[6]||(a[6]=e=>De.value.priceMax=e),type:"number",placeholder:"最高价",class:"price-input",min:"0"},null,512),[[m,De.value.priceMax,void 0,{number:!0}]])])])])):d("",!0),u("div",pe,[u("div",ge,[a[20]||(a[20]=u("label",{class:"advanced-label"},"标签",-1)),u("div",he,[u("div",me,[(R(!0),c(w,null,b(Be.value,e=>(R(),c("span",{key:e.id,class:"selected-tag"},[q(x(e.name)+" ",1),u("button",{onClick:a=>(e=>{Be.value=Be.value.filter(a=>a.id!==e.id)})(e),class:"remove-tag"},[v(g(f),{class:"icon"})],8,ye)]))),128))]),p(u("input",{"onUpdate:modelValue":a[7]||(a[7]=e=>Ie.value=e),type:"text",placeholder:"输入标签名称...",class:"tag-input",onInput:aa},null,544),[[m,Ie.value]]),Pe.value.length>0?(R(),c("div",fe,[(R(!0),c(w,null,b(Pe.value,e=>(R(),c("div",{key:e.id,class:"tag-option",onClick:a=>(e=>{Be.value.find(a=>a.id===e.id)||Be.value.push(e),Ie.value="",Pe.value=[]})(e)},[u("span",{class:"tag-color",style:$({backgroundColor:e.color})},null,4),q(" "+x(e.name),1)],8,ke))),128))])):d("",!0)])])]),u("div",{class:"advanced-actions"},[u("button",{onClick:Ze,class:"reset-button"},"重置"),u("button",{onClick:ea,class:"apply-button"},"应用筛选")])])])):d("",!0),Oe.value?(R(),c("div",we,a[21]||(a[21]=[u("div",{class:"search-loading"},[u("div",{class:"loading-spinner"}),u("span",null,"搜索中...")],-1)]))):d("",!0),Ue.value?(R(),c("div",be," 找到 "+x(Ue.value.total)+" 个结果，用时 "+x(Ue.value.searchTime)+"ms ",1)):d("",!0)],2))}}),[["__scopeId","data-v-aa9aa83a"]]);export{_e as E,F as s};
