import{d as e,r as t,b as l,o as s,m as a,a8 as r,q as o,y as i,N as n,u as d,P as c,x as u,A as m,B as p,D as g,F as b,z as x,C as f,a7 as y,t as v,X as h,aK as w,a3 as k,v as _,aI as C,af as j,G as U}from"./vendor-VsOxy-e0.js";const I={class:"min-h-screen bg-gray-50"},V={class:"max-w-4xl mx-auto px-4 sm:px-6 lg:px-8 py-12"},q={class:"bg-blue-50 border border-blue-200 rounded-lg p-6 mb-8"},P={class:"text-lg font-semibold text-blue-900 mb-3 flex items-center"},A={class:"bg-white rounded-lg shadow-md p-8"},R={class:"grid grid-cols-1 md:grid-cols-2 gap-6"},z={class:"md:col-span-2"},D=["value"],F={class:"mb-6"},G={class:"mt-1 text-sm text-gray-500"},K={class:"mt-1 text-sm text-gray-500"},L={class:"mb-6"},N={class:"border-2 border-dashed border-gray-300 rounded-lg p-6 text-center"},$={key:0,class:"space-y-2"},B={key:1,class:"space-y-2"},E=["src"],J={class:"grid grid-cols-2 md:grid-cols-4 gap-4"},O=["src"],S=["onClick"],T={class:"space-y-4"},H={class:"flex space-x-2"},M={key:0,class:"flex flex-wrap gap-2"},Q=["onClick"],W={class:"flex justify-end space-x-4 pt-6 border-t border-gray-200"},X=["disabled"],Y={class:"mt-12 bg-gray-100 rounded-lg p-6"},Z={class:"text-lg font-semibold text-gray-900 mb-4 flex items-center"},ee=e({__name:"ProductUploadView",setup(e){const ee=t([{id:1,name:"开发工具"},{id:2,name:"设计工具"},{id:3,name:"办公软件"},{id:4,name:"学习资源"},{id:5,name:"其他工具"}]),te=l({name:"",category_id:"",type:"",price:0,url:"",description:"",content:"",main_image:"",additional_images:[],tags:[]}),le=t(!1),se=t(""),ae=e=>{const t=e.target;if(t.files&&t.files[0]){const e=t.files[0],l=new FileReader;l.onload=e=>{var t;te.main_image=null==(t=e.target)?void 0:t.result},l.readAsDataURL(e)}},re=e=>{const t=e.target;if(t.files){Array.from(t.files).forEach(e=>{if(te.additional_images.length<4){const t=new FileReader;t.onload=e=>{var t;te.additional_images.push(null==(t=e.target)?void 0:t.result)},t.readAsDataURL(e)}})}},oe=()=>{se.value.trim()&&!te.tags.includes(se.value.trim())&&(te.tags.push(se.value.trim()),se.value="")},ie=()=>{Object.assign(te,{name:"",category_id:"",type:"",price:0,url:"",description:"",content:"",main_image:"",additional_images:[],tags:[]}),se.value=""},ne=async()=>{if(te.name&&te.category_id&&te.type&&te.description&&te.main_image){le.value=!0;try{console.log("提交产品:",te),await new Promise(e=>setTimeout(e,2e3)),alert("产品提交成功！我们会在1-3个工作日内完成审核。"),ie()}catch(e){console.error("提交失败:",e),alert("提交失败，请稍后重试")}finally{le.value=!1}}else alert("请填写必填字段")};return s(()=>{document.title="产品上传 - 工具导航站"}),(e,t)=>(U(),a("div",I,[t[34]||(t[34]=r('<div class="bg-white shadow-sm"><div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8"><div class="text-center"><h1 class="text-3xl font-bold text-gray-900">产品上传</h1><p class="mt-4 text-lg text-gray-600"> 分享您的优质产品，让更多用户发现和使用 </p></div></div></div>',1)),o("div",V,[o("div",q,[o("h2",P,[i(d(c),{class:"w-5 h-5 mr-2"}),t[11]||(t[11]=n(" 上传须知 "))]),t[12]||(t[12]=o("ul",{class:"text-sm text-blue-800 space-y-2"},[o("li",null,"• 产品必须是原创或拥有合法授权"),o("li",null,"• 提供详细的产品描述和使用说明"),o("li",null,"• 上传高质量的产品截图或演示视频"),o("li",null,"• 设置合理的价格，支持免费产品"),o("li",null,"• 审核通过后产品将在1-3个工作日内上线")],-1))]),o("div",A,[o("form",{onSubmit:u(ne,["prevent"]),class:"space-y-8"},[o("div",null,[t[20]||(t[20]=o("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"基本信息",-1)),o("div",R,[o("div",z,[t[13]||(t[13]=o("label",{for:"name",class:"block text-sm font-medium text-gray-700 mb-2"},[n(" 产品名称 "),o("span",{class:"text-red-500"},"*")],-1)),m(o("input",{id:"name","onUpdate:modelValue":t[0]||(t[0]=e=>te.name=e),type:"text",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"请输入产品名称"},null,512),[[p,te.name]])]),o("div",null,[t[15]||(t[15]=o("label",{for:"category",class:"block text-sm font-medium text-gray-700 mb-2"},[n(" 产品分类 "),o("span",{class:"text-red-500"},"*")],-1)),m(o("select",{id:"category","onUpdate:modelValue":t[1]||(t[1]=e=>te.category_id=e),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},[t[14]||(t[14]=o("option",{value:""},"请选择分类",-1)),(U(!0),a(b,null,x(ee.value,e=>(U(),a("option",{key:e.id,value:e.id},f(e.name),9,D))),128))],512),[[g,te.category_id]])]),o("div",null,[t[17]||(t[17]=o("label",{for:"type",class:"block text-sm font-medium text-gray-700 mb-2"},[n(" 产品类型 "),o("span",{class:"text-red-500"},"*")],-1)),m(o("select",{id:"type","onUpdate:modelValue":t[2]||(t[2]=e=>te.type=e),required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent"},t[16]||(t[16]=[r('<option value="">请选择类型</option><option value="software">软件工具</option><option value="template">模板素材</option><option value="course">教程课程</option><option value="service">服务</option><option value="other">其他</option>',6)]),512),[[g,te.type]])]),o("div",null,[t[18]||(t[18]=o("label",{for:"price",class:"block text-sm font-medium text-gray-700 mb-2"},[n(" 价格（元） "),o("span",{class:"text-red-500"},"*")],-1)),m(o("input",{id:"price","onUpdate:modelValue":t[3]||(t[3]=e=>te.price=e),type:"number",min:"0",step:"0.01",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"0.00（免费请输入0）"},null,512),[[p,te.price,void 0,{number:!0}]])]),o("div",null,[t[19]||(t[19]=o("label",{for:"url",class:"block text-sm font-medium text-gray-700 mb-2"}," 产品链接 ",-1)),m(o("input",{id:"url","onUpdate:modelValue":t[4]||(t[4]=e=>te.url=e),type:"url",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"https://example.com"},null,512),[[p,te.url]])])])]),o("div",null,[t[23]||(t[23]=o("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"产品描述",-1)),o("div",F,[t[21]||(t[21]=o("label",{for:"description",class:"block text-sm font-medium text-gray-700 mb-2"},[n(" 简短描述 "),o("span",{class:"text-red-500"},"*")],-1)),m(o("textarea",{id:"description","onUpdate:modelValue":t[5]||(t[5]=e=>te.description=e),rows:"3",required:"",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"请简要描述您的产品特点和用途..."},null,512),[[p,te.description]]),o("div",G,f(te.description.length)+"/200 字符 ",1)]),o("div",null,[t[22]||(t[22]=o("label",{for:"content",class:"block text-sm font-medium text-gray-700 mb-2"}," 详细介绍 ",-1)),m(o("textarea",{id:"content","onUpdate:modelValue":t[6]||(t[6]=e=>te.content=e),rows:"8",class:"w-full px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent resize-none",placeholder:"请详细介绍产品功能、使用方法、适用场景等..."},null,512),[[p,te.content]]),o("div",K,f(te.content.length)+"/2000 字符 ",1)])]),o("div",null,[t[29]||(t[29]=o("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"产品图片",-1)),o("div",L,[t[26]||(t[26]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"},[n(" 产品主图 "),o("span",{class:"text-red-500"},"*")],-1)),o("div",N,[te.main_image?(U(),a("div",B,[o("img",{src:te.main_image,alt:"产品主图",class:"w-32 h-24 object-cover mx-auto rounded"},null,8,E),o("button",{type:"button",onClick:t[7]||(t[7]=e=>te.main_image=""),class:"text-red-600 hover:text-red-800 text-sm"}," 删除图片 ")])):(U(),a("div",$,[i(d(y),{class:"w-12 h-12 text-gray-400 mx-auto"}),t[24]||(t[24]=o("p",{class:"text-sm text-gray-600"},"点击上传产品主图",-1)),t[25]||(t[25]=o("p",{class:"text-xs text-gray-500"},"支持 JPG、PNG 格式，建议尺寸 800x600",-1))])),o("input",{type:"file",accept:"image/*",class:"hidden",ref:"mainImageInput",onChange:ae},null,544),o("button",{type:"button",onClick:t[8]||(t[8]=t=>e.$refs.mainImageInput.click()),class:"mt-2 text-blue-600 hover:text-blue-800 text-sm font-medium"},f(te.main_image?"更换图片":"选择图片"),1)])]),o("div",null,[t[28]||(t[28]=o("label",{class:"block text-sm font-medium text-gray-700 mb-2"}," 附加图片（可选） ",-1)),o("div",J,[(U(!0),a(b,null,x(te.additional_images,(e,t)=>(U(),a("div",{key:t,class:"relative border border-gray-300 rounded-lg p-2"},[o("img",{src:e,alt:"附加图片",class:"w-full h-20 object-cover rounded"},null,8,O),o("button",{type:"button",onClick:e=>(e=>{te.additional_images.splice(e,1)})(t),class:"absolute -top-2 -right-2 w-6 h-6 bg-red-500 text-white rounded-full flex items-center justify-center text-xs hover:bg-red-600"},[i(d(h),{class:"w-3 h-3"})],8,S)]))),128)),te.additional_images.length<4?(U(),a("div",{key:0,class:"border-2 border-dashed border-gray-300 rounded-lg p-4 flex flex-col items-center justify-center cursor-pointer hover:border-gray-400",onClick:t[9]||(t[9]=t=>e.$refs.additionalImageInput.click())},[i(d(w),{class:"w-6 h-6 text-gray-400 mb-1"}),t[27]||(t[27]=o("span",{class:"text-xs text-gray-500"},"添加图片",-1))])):v("",!0)]),o("input",{type:"file",accept:"image/*",multiple:"",class:"hidden",ref:"additionalImageInput",onChange:re},null,544)])]),o("div",null,[t[31]||(t[31]=o("h3",{class:"text-xl font-semibold text-gray-900 mb-6"},"产品标签",-1)),o("div",T,[o("div",null,[t[30]||(t[30]=o("label",{for:"tags",class:"block text-sm font-medium text-gray-700 mb-2"}," 添加标签 ",-1)),o("div",H,[m(o("input",{"onUpdate:modelValue":t[10]||(t[10]=e=>se.value=e),type:"text",class:"flex-1 px-3 py-2 border border-gray-300 rounded-lg focus:ring-2 focus:ring-blue-500 focus:border-transparent",placeholder:"输入标签名称",onKeyup:k(oe,["enter"])},null,544),[[p,se.value]]),o("button",{type:"button",onClick:oe,class:"px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors"}," 添加 ")])]),te.tags.length>0?(U(),a("div",M,[(U(!0),a(b,null,x(te.tags,(e,t)=>(U(),a("span",{key:t,class:"inline-flex items-center px-3 py-1 bg-blue-100 text-blue-800 rounded-full text-sm"},[n(f(e)+" ",1),o("button",{type:"button",onClick:e=>(e=>{te.tags.splice(e,1)})(t),class:"ml-2 text-blue-600 hover:text-blue-800"},[i(d(h),{class:"w-3 h-3"})],8,Q)]))),128))])):v("",!0)])]),o("div",W,[o("button",{type:"button",onClick:ie,class:"px-6 py-2 border border-gray-300 text-gray-700 rounded-lg hover:bg-gray-50 transition-colors"}," 重置 "),o("button",{type:"submit",disabled:le.value,class:"px-6 py-3 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50 disabled:cursor-not-allowed transition-colors flex items-center"},[le.value?(U(),_(d(C),{key:0,class:"w-4 h-4 mr-2 animate-spin"})):v("",!0),n(" "+f(le.value?"提交中...":"提交审核"),1)],8,X)])],32)]),o("div",Y,[o("h3",Z,[i(d(j),{class:"w-5 h-5 mr-2 text-gray-600"}),t[32]||(t[32]=n(" 审核流程 "))]),t[33]||(t[33]=r('<div class="grid grid-cols-1 md:grid-cols-4 gap-4 text-sm"><div class="text-center"><div class="w-8 h-8 bg-blue-100 text-blue-600 rounded-full flex items-center justify-center mx-auto mb-2 font-semibold">1</div><p class="font-medium">提交产品</p><p class="text-gray-600">填写完整信息</p></div><div class="text-center"><div class="w-8 h-8 bg-yellow-100 text-yellow-600 rounded-full flex items-center justify-center mx-auto mb-2 font-semibold">2</div><p class="font-medium">初步审核</p><p class="text-gray-600">1个工作日内</p></div><div class="text-center"><div class="w-8 h-8 bg-purple-100 text-purple-600 rounded-full flex items-center justify-center mx-auto mb-2 font-semibold">3</div><p class="font-medium">详细审核</p><p class="text-gray-600">2-3个工作日</p></div><div class="text-center"><div class="w-8 h-8 bg-green-100 text-green-600 rounded-full flex items-center justify-center mx-auto mb-2 font-semibold">4</div><p class="font-medium">上线发布</p><p class="text-gray-600">审核通过后</p></div></div>',1))])])]))}});export{ee as default};
