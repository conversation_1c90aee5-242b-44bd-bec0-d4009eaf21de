<!doctype html>
<html lang="zh-CN">
  <head>
    <meta charset="UTF-8" />
    <meta name="viewport" content="width=device-width, initial-scale=1.0" />

    <!-- SEO 基础标签 -->
    <title>Ramusi 工具导航站 - 精选开发设计AI工具，提升工作效率</title>
    <meta
      name="description"
      content="Ramusi 工具导航站，精选收录1000+优质工具：开发工具、设计软件、AI应用、效率工具、在线服务。一站式工具发现平台，让工作更高效。"
    />
    <meta
      name="keywords"
      content="工具导航,开发工具,设计工具,AI工具,效率工具,在线工具,软件推荐,工具集合,ramusi"
    />
    <meta name="author" content="Ramusi Team" />
    <meta name="robots" content="index, follow" />
    <link rel="canonical" href="https://ramusi.cn" />

    <!-- Open Graph 标签 -->
    <meta
      property="og:title"
      content="Ramusi 工具导航站 - 精选开发设计AI工具"
    />
    <meta
      property="og:description"
      content="精选收录1000+优质工具，一站式工具发现平台，让工作更高效。"
    />
    <meta property="og:type" content="website" />
    <meta property="og:url" content="https://ramusi.cn" />
    <meta property="og:image" content="https://ramusi.cn/og-image.png" />
    <meta property="og:site_name" content="Ramusi 工具导航站" />
    <meta property="og:locale" content="zh_CN" />

    <!-- Twitter Card 标签 -->
    <meta name="twitter:card" content="summary_large_image" />
    <meta
      name="twitter:title"
      content="Ramusi 工具导航站 - 精选开发设计AI工具"
    />
    <meta
      name="twitter:description"
      content="精选收录1000+优质工具，一站式工具发现平台，让工作更高效。"
    />
    <meta name="twitter:image" content="https://ramusi.cn/og-image.png" />

    <!-- 移动端优化 -->
    <meta name="format-detection" content="telephone=no" />
    <meta name="apple-mobile-web-app-capable" content="yes" />
    <meta
      name="apple-mobile-web-app-status-bar-style"
      content="black-translucent"
    />
    <meta name="apple-mobile-web-app-title" content="Ramusi工具站" />

    <!-- 网站图标 -->
    <link rel="icon" type="image/x-icon" href="/favicon.ico" />
    <link rel="apple-touch-icon" sizes="180x180" href="/apple-touch-icon.png" />
    <link rel="icon" type="image/png" sizes="32x32" href="/favicon-32x32.png" />
    <link rel="icon" type="image/png" sizes="16x16" href="/favicon-16x16.png" />
    <link rel="manifest" href="./site.webmanifest" />

    <!-- 结构化数据 -->
    <script type="application/ld+json">
      {
        "@context": "https://schema.org",
        "@type": "WebSite",
        "name": "Ramusi 工具导航站",
        "url": "https://ramusi.cn",
        "description": "精选收录1000+优质工具，一站式工具发现平台，让工作更高效。",
        "potentialAction": {
          "@type": "SearchAction",
          "target": "https://ramusi.cn/search?q={search_term_string}",
          "query-input": "required name=search_term_string"
        }
      }
    </script>
    <style>
      * {
        margin: 0;
        padding: 0;
        box-sizing: border-box;
      }

      body {
        font-family: "Segoe UI", Tahoma, Geneva, Verdana, sans-serif;
        background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
        min-height: 100vh;
      }

      #app {
        min-height: 100vh;
      }

      .loading {
        display: flex;
        align-items: center;
        justify-content: center;
        min-height: 100vh;
        color: white;
        font-size: 18px;
      }
    </style>
    <script type="module" crossorigin src="./assets/index-DdsLZGoA.js"></script>
    <link rel="modulepreload" crossorigin href="./assets/vendor-VsOxy-e0.js">
    <link rel="stylesheet" crossorigin href="./assets/index-BukxQyzs.css">
  </head>
  <body>
    <div id="app">
      <div class="loading">正在加载工具导航站...</div>
    </div>
    <script>
      // 错误处理
      window.addEventListener("error", function (e) {
        console.error("页面加载错误:", e.error);
        const app = document.getElementById("app");
        if (app) {
          app.innerHTML = '<div class="loading">加载失败，请刷新页面重试</div>';
        }
      });

      // 检查资源加载
      setTimeout(function () {
        const app = document.getElementById("app");
        if (app && app.innerHTML.includes("正在加载")) {
          console.warn("Vue 应用可能未正确加载");
        }
      }, 3000);
    </script>
  </body>
</html>
