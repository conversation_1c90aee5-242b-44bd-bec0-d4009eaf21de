name: Health Check

on:
  push:
    branches: [main]
  schedule:
    - cron: "0 */6 * * *" # 每6小时运行一次
  workflow_dispatch:

jobs:
  health-check:
    runs-on: ubuntu-latest
    name: Website Health Check
    steps:
      - name: Check website accessibility
        run: |
          echo "🔍 检查网站可访问性..."
          response=$(curl -s -o /dev/null -w "%{http_code}" https://ramusi.cn)
          if [ "$response" = "200" ]; then
            echo "✅ 网站正常访问 (HTTP $response)"
          else
            echo "❌ 网站访问异常 (HTTP $response)"
            exit 1
          fi

      - name: Check basic functionality
        run: |
          echo "🔍 检查基本功能..."
          content=$(curl -s https://ramusi.cn)
          if echo "$content" | grep -q "Ramusi"; then
            echo "✅ 网站内容正常"
          else
            echo "❌ 网站内容异常"
            exit 1
          fi

      - name: Health check summary
        run: |
          echo "📊 健康检查完成"
          echo "✅ 网站可访问性: 正常"
          echo "✅ 基本功能: 正常"
